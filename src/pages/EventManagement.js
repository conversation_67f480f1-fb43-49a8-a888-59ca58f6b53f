import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  FiArrowLeft,
  FiSearch,
  FiRefreshCw,
  FiAlertCircle,
  FiX,
  FiArrowUp,
  FiActivity,
  FiDatabase,
  FiFilter,
  FiColumns,
  FiChevronUp,
  FiChevronDown,
} from "react-icons/fi";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import "../styles/EventManagement.css";

const EventManagement = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [events, setEvents] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Pagination state
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const PAGE_SIZE = 100;

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [productLineFilter, setProductLineFilter] = useState("");
  const [screenFilter, setScreenFilter] = useState("");

  // Sorting state
  const [sortField, setSortField] = useState("");
  const [sortDirection, setSortDirection] = useState("asc");

  // Column visibility state
  const [visibleColumns, setVisibleColumns] = useState([]);
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const [allColumns, setAllColumns] = useState([]);

  // Modal state
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showModal, setShowModal] = useState(false);

  // Refs for infinite scroll
  const containerRef = useRef(null);

  // Back to top button state
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Initialize from URL params and fetch initial data
  useEffect(() => {
    document.title = "Event Management";

    // Initialize filters from URL params
    const urlSearch = searchParams.get("search") || "";
    const urlStatus = searchParams.get("status") || "";
    const urlProductLine = searchParams.get("product_line") || "";
    const urlScreen = searchParams.get("screen") || "";
    const urlSort = searchParams.get("sort") || "";
    const urlSortDir = searchParams.get("sort_dir") || "asc";

    setSearchQuery(urlSearch);
    setStatusFilter(urlStatus);
    setProductLineFilter(urlProductLine);
    setScreenFilter(urlScreen);
    setSortField(urlSort);
    setSortDirection(urlSortDir);

    // Initialize default visible columns
    const defaultColumns = [
      "event_id",
      "event_name",
      "event_status",
      "screen_id",
      "screen_name",
      "screen_status",
      "product_line",
      "app_id",
    ];
    setVisibleColumns(defaultColumns);

    fetchEvents(
      1,
      true,
      urlSearch,
      urlStatus,
      urlProductLine,
      urlScreen,
      urlSort,
      urlSortDir
    );
  }, []);

  // Handle scroll for back to top button
  useEffect(() => {
    const handleScroll = () => {
      const scrollThreshold = window.innerHeight * 3;
      setShowBackToTop(window.scrollY > scrollThreshold);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Setup intersection observer for infinite scrolling
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (
          entries[0].isIntersecting &&
          hasMore &&
          !loadingMore &&
          events.length > 0
        ) {
          loadMoreEvents();
        }
      },
      { threshold: 0.1, rootMargin: "100px" }
    );

    const timer = setTimeout(() => {
      if (containerRef.current) {
        observer.observe(containerRef.current);
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, [hasMore, loadingMore, events.length]);

  // Fetch events from API with pagination, filtering, and search
  const fetchEvents = async (
    pageNum = 1,
    reset = false,
    searchTerm = searchQuery,
    statusFilterValue = statusFilter,
    productLineFilterValue = productLineFilter,
    screenFilterValue = screenFilter,
    sortFieldValue = sortField,
    sortDirectionValue = sortDirection
  ) => {
    if (pageNum === 1) {
      setIsLoading(true);
    } else {
      setLoadingMore(true);
    }

    if (reset) {
      setError(null);
    }

    try {
      // Prepare URL parameters for GET request
      const params = {
        page: pageNum,
        limit: PAGE_SIZE,
      };

      // Add filters if they exist
      if (searchTerm) {
        params.search = searchTerm;
      }
      if (statusFilterValue) {
        params.event_status = statusFilterValue;
      }
      if (productLineFilterValue) {
        params.product_line = productLineFilterValue;
      }
      if (screenFilterValue) {
        params.screen = screenFilterValue;
      }
      if (sortFieldValue) {
        params.sort = sortFieldValue;
        params.sort_dir = sortDirectionValue;
      }

      // Update URL params
      const newSearchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value && key !== "page" && key !== "limit") {
          newSearchParams.set(key, value);
        }
      });
      setSearchParams(newSearchParams);

      const response = await apiService.wlpGet(
        ENDPOINTS.FUNNEL.GET_ALL_EVENTS,
        params
      );

      let data = [];
      let total = 0;

      // Process API response
      if (response && response.result && Array.isArray(response.result)) {
        data = response.result;
        total = response.totalrows || 0;
      } else {
        throw new Error("Unexpected response format");
      }

      // Extract all available columns from the first record
      if (data.length > 0 && allColumns.length === 0) {
        const columns = Object.keys(data[0]).filter(
          (key) => !["id"].includes(key) // Exclude internal fields
        );
        setAllColumns(columns);

        // Set default visible columns if not already set
        if (visibleColumns.length === 0) {
          const defaultColumns = columns.slice(0, 8); // Show first 8 columns by default
          setVisibleColumns(defaultColumns);
        }
      }

      // Check if there are more items
      setHasMore(data.length >= PAGE_SIZE && pageNum * PAGE_SIZE < total);
      setTotalRows(total);

      // Update state
      if (reset || pageNum === 1) {
        setEvents(data);
        setPage(1);
      } else {
        setEvents((prevEvents) => [...prevEvents, ...data]);
        setPage(pageNum);
      }
    } catch (error) {
      console.error("Error fetching events:", error);
      if (reset || pageNum === 1) {
        setError("Failed to load events. Please try again.");
        setEvents([]);
        setHasMore(false);
      }
    } finally {
      if (pageNum === 1) {
        setIsLoading(false);
      } else {
        setLoadingMore(false);
      }
    }
  };

  // Load more events for infinite scrolling
  const loadMoreEvents = () => {
    if (!loadingMore && hasMore && events.length > 0) {
      fetchEvents(
        page + 1,
        false,
        searchQuery,
        statusFilter,
        productLineFilter,
        screenFilter,
        sortField,
        sortDirection
      );
    }
  };

  // Handle sorting
  const handleSort = (field) => {
    let newDirection = "asc";
    if (sortField === field && sortDirection === "asc") {
      newDirection = "desc";
    }
    setSortField(field);
    setSortDirection(newDirection);
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(
      1,
      true,
      searchQuery,
      statusFilter,
      productLineFilter,
      screenFilter,
      field,
      newDirection
    );
  };

  // Handle row click
  const handleRowClick = (event) => {
    setSelectedEvent(event);
    setShowModal(true);
  };

  // Handle column visibility toggle
  const toggleColumnVisibility = (column) => {
    setVisibleColumns((prev) => {
      if (prev.includes(column)) {
        return prev.filter((col) => col !== column);
      } else {
        return [...prev, column];
      }
    });
  };

  // Debounce search function
  const useDebounce = (callback, delay) => {
    const timeoutRef = useRef(null);

    return (searchQuery) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(searchQuery);
      }, delay);
    };
  };

  // Handle search
  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    debouncedSearch(query);
  };

  // Debounced search
  const debouncedSearch = useDebounce((searchQuery) => {
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(
      1,
      true,
      searchQuery,
      statusFilter,
      productLineFilter,
      screenFilter,
      sortField,
      sortDirection
    );
  }, 500);

  // Handle status filter change
  const handleStatusFilterChange = (status) => {
    setStatusFilter(status);
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(
      1,
      true,
      searchQuery,
      status,
      productLineFilter,
      screenFilter,
      sortField,
      sortDirection
    );
  };

  // Handle screen filter change
  const handleScreenFilterChange = (screen) => {
    setScreenFilter(screen);
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(
      1,
      true,
      searchQuery,
      statusFilter,
      productLineFilter,
      screen,
      sortField,
      sortDirection
    );
  };

  // Clear all filters
  const clearFilters = () => {
    const emptySearch = "";
    const emptyStatus = "";
    const emptyProductLine = "";
    const emptyScreen = "";
    const emptySort = "";
    const emptySortDir = "asc";

    setSearchQuery(emptySearch);
    setStatusFilter(emptyStatus);
    setProductLineFilter(emptyProductLine);
    setScreenFilter(emptyScreen);
    setSortField(emptySort);
    setSortDirection(emptySortDir);
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    setSearchParams(new URLSearchParams()); // Clear URL params
    fetchEvents(
      1,
      true,
      emptySearch,
      emptyStatus,
      emptyProductLine,
      emptyScreen,
      emptySort,
      emptySortDir
    );
  };

  // Refresh page function
  const refreshPage = () => {
    setPage(1);
    setEvents([]);
    setHasMore(true);
    setIsLoading(true);
    fetchEvents(
      1,
      true,
      searchQuery,
      statusFilter,
      productLineFilter,
      screenFilter,
      sortField,
      sortDirection
    );
    setSuccessMessage("Page refreshed");
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  // Format array data for display
  const formatArrayData = (data) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return "N/A";
    }
    return data.join(", ");
  };

  // Render cell value based on column type
  const renderCellValue = (value, column) => {
    if (value === null || value === undefined) {
      return "N/A";
    }

    // Handle status columns
    if (column.includes("status") && typeof value === "boolean") {
      return (
        <span className={`status-badge ${value ? "active" : "inactive"}`}>
          {value ? "Active" : "Inactive"}
        </span>
      );
    }

    // Handle array data
    if (Array.isArray(value)) {
      return formatArrayData(value);
    }

    // Handle objects
    if (typeof value === "object") {
      return JSON.stringify(value);
    }

    // Handle long text
    if (typeof value === "string" && value.length > 50) {
      return <span title={value}>{value.substring(0, 47)}...</span>;
    }

    return value.toString();
  };

  return (
    <div className="users-content-container">
      {/* <div className="event-management-header">
        <button
          type="button"
          className="back-button"
          onClick={() => navigate("/dashboard")}
        >
          <FiArrowLeft /> Back
        </button>
        <h2 className="event-management-title">Event Management</h2>
      </div> */}

      {successMessage && (
        <div className="success-message">
          <FiActivity />
          <span>{successMessage}</span>
        </div>
      )}

      {/* Back to Top Button */}
      {showBackToTop && (
        <button
          className="back-to-top-button"
          onClick={scrollToTop}
          aria-label="Back to top"
        >
          <FiArrowUp />
        </button>
      )}

      {/* Filter and Search Bar */}
      <div className="filter-search-bar">
        <div className="filter-container">
          <label htmlFor="status-filter">Status:</label>
          <select
            id="status-filter"
            value={statusFilter}
            onChange={(e) => handleStatusFilterChange(e.target.value)}
            className="filter-select"
          >
            <option value="">All</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </select>
        </div>

        <div className="search-container">
          {isLoading && searchQuery ? (
            <FiRefreshCw className="search-icon spinning" />
          ) : (
            <FiSearch className="search-icon" />
          )}
          <input
            type="text"
            placeholder="Search events by ID or name..."
            value={searchQuery}
            onChange={handleSearch}
            className="search-input"
          />
          {searchQuery && (
            <FiX
              className="clear-search-icon"
              onClick={() => {
                setSearchQuery("");
                setPage(1);
                setEvents([]);
                setHasMore(true);
                setIsLoading(true);
                fetchEvents(
                  1,
                  true,
                  "",
                  statusFilter,
                  productLineFilter,
                  screenFilter,
                  sortField,
                  sortDirection
                );
              }}
            />
          )}
        </div>

        <div className="actions-container">
          <button
            type="button"
            className="btn-secondary"
            onClick={() => setShowColumnSelector(!showColumnSelector)}
            title="Select Columns"
          >
            <FiColumns /> Columns
          </button>
          {(searchQuery ||
            statusFilter ||
            productLineFilter ||
            screenFilter) && (
            <button className="clear-filters-btn" onClick={clearFilters}>
              <FiFilter /> Clear Filters
            </button>
          )}
          <button
            type="button"
            className="btn-primary"
            onClick={refreshPage}
            title="Refresh Page"
          >
            <FiRefreshCw /> Refresh
          </button>
        </div>
      </div>

      {/* Column Selector Dropdown */}
      {showColumnSelector && (
        <div className="column-selector-dropdown">
          <div className="column-selector-header">
            <h4>Select Columns to Display</h4>
            <button
              className="close-selector-btn"
              onClick={() => setShowColumnSelector(false)}
            >
              <FiX />
            </button>
          </div>
          <div className="column-selector-content">
            {allColumns.map((column) => (
              <label key={column} className="column-checkbox">
                <input
                  type="checkbox"
                  checked={visibleColumns.includes(column)}
                  onChange={() => toggleColumnVisibility(column)}
                />
                <span>
                  {column
                    .replace(/_/g, " ")
                    .replace(/\b\w/g, (l) => l.toUpperCase())}
                </span>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* Results Summary */}
      {!isLoading && events.length > 0 && (
        <div className="results-summary">
          <span>
            Showing {events.length} of {totalRows} events
            {hasMore && " (loading more as you scroll)"}
            {sortField && ` • Sorted by ${sortField} (${sortDirection})`}
          </span>
        </div>
      )}

      {error && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{error}</span>
        </div>
      )}

      {isLoading && events.length === 0 ? (
        <div className="loading-spinner"></div>
      ) : events.length === 0 ? (
        <div className="empty-state">
          <FiDatabase className="empty-icon" />
          <p>No events found</p>
          {(searchQuery ||
            statusFilter ||
            productLineFilter ||
            screenFilter) && (
            <button className="btn-primary" onClick={clearFilters}>
              Clear Filters
            </button>
          )}
        </div>
      ) : (
        <>
          <div className="users-table-container">
            <table className="users-table">
              <thead>
                <tr>
                  {visibleColumns.map((column) => (
                    <th
                      key={column}
                      className="sortable-header"
                      onClick={() => handleSort(column)}
                    >
                      <div className="header-content">
                        <span>
                          {column
                            .replace(/_/g, " ")
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </span>
                        {sortField === column && (
                          <span className="sort-indicator">
                            {sortDirection === "asc" ? (
                              <FiChevronUp />
                            ) : (
                              <FiChevronDown />
                            )}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {events.map((event) => (
                  <tr
                    key={event.id}
                    className="clickable-row"
                    onClick={() => handleRowClick(event)}
                  >
                    {visibleColumns.map((column) => (
                      <td key={column}>
                        {renderCellValue(event[column], column)}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Infinite scroll loading indicator */}
          {loadingMore && (
            <div className="loading-more">
              <div className="loading-spinner small"></div>
              <span>Loading more events...</span>
            </div>
          )}

          {/* Sentinel element for infinite scroll */}
          <div ref={containerRef} className="scroll-sentinel"></div>
        </>
      )}

      {/* Event Details Modal */}
      {showModal && selectedEvent && (
        <div className="modal-overlay" onClick={() => setShowModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Event Details</h3>
              <button
                className="modal-close-btn"
                onClick={() => setShowModal(false)}
              >
                <FiX />
              </button>
            </div>
            <div className="modal-body">
              <div className="users-details-grid">
                {Object.entries(selectedEvent).map(([key, value]) => (
                  <div key={key} className="detail-item">
                    <label className="detail-label">
                      {key
                        .replace(/_/g, " ")
                        .replace(/\b\w/g, (l) => l.toUpperCase())}
                      :
                    </label>
                    <div className="detail-value">
                      {renderCellValue(value, key)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="btn-secondary"
                onClick={() => setShowModal(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EventManagement;
