/* ===================================
   UTILITY CLASSES
   ================================= */

/* ===== SPACING UTILITIES ===== */

/* Margin utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }
.m-6 { margin: var(--spacing-2xl); }
.m-8 { margin: var(--spacing-3xl); }

/* Margin top */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }
.mt-6 { margin-top: var(--spacing-2xl); }
.mt-8 { margin-top: var(--spacing-3xl); }

/* Margin bottom */
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }
.mb-6 { margin-bottom: var(--spacing-2xl); }
.mb-8 { margin-bottom: var(--spacing-3xl); }

/* Margin left */
.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-md); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }
.ml-6 { margin-left: var(--spacing-2xl); }
.ml-8 { margin-left: var(--spacing-3xl); }

/* Margin right */
.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-md); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }
.mr-6 { margin-right: var(--spacing-2xl); }
.mr-8 { margin-right: var(--spacing-3xl); }

/* Margin horizontal */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--spacing-xs); margin-right: var(--spacing-xs); }
.mx-2 { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx-3 { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
.mx-4 { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
.mx-5 { margin-left: var(--spacing-xl); margin-right: var(--spacing-xl); }
.mx-6 { margin-left: var(--spacing-2xl); margin-right: var(--spacing-2xl); }
.mx-auto { margin-left: auto; margin-right: auto; }

/* Margin vertical */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--spacing-xs); margin-bottom: var(--spacing-xs); }
.my-2 { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my-3 { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-4 { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-5 { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }
.my-6 { margin-top: var(--spacing-2xl); margin-bottom: var(--spacing-2xl); }

/* Padding utilities */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }
.p-6 { padding: var(--spacing-2xl); }
.p-8 { padding: var(--spacing-3xl); }

/* Padding top */
.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--spacing-xs); }
.pt-2 { padding-top: var(--spacing-sm); }
.pt-3 { padding-top: var(--spacing-md); }
.pt-4 { padding-top: var(--spacing-lg); }
.pt-5 { padding-top: var(--spacing-xl); }
.pt-6 { padding-top: var(--spacing-2xl); }

/* Padding bottom */
.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--spacing-xs); }
.pb-2 { padding-bottom: var(--spacing-sm); }
.pb-3 { padding-bottom: var(--spacing-md); }
.pb-4 { padding-bottom: var(--spacing-lg); }
.pb-5 { padding-bottom: var(--spacing-xl); }
.pb-6 { padding-bottom: var(--spacing-2xl); }

/* Padding horizontal */
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-2 { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-3 { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-4 { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-5 { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }
.px-6 { padding-left: var(--spacing-2xl); padding-right: var(--spacing-2xl); }

/* Padding vertical */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-2 { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-3 { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-4 { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-5 { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }
.py-6 { padding-top: var(--spacing-2xl); padding-bottom: var(--spacing-2xl); }

/* ===== DISPLAY UTILITIES ===== */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }
.d-grid { display: grid; }
.d-table { display: table; }

/* ===== FLEXBOX UTILITIES ===== */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.items-baseline { align-items: baseline; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-grow { flex-grow: 1; }

/* ===== GRID UTILITIES ===== */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }
.gap-5 { gap: var(--spacing-xl); }
.gap-6 { gap: var(--spacing-2xl); }

/* ===== POSITION UTILITIES ===== */
.position-static { position: static; }
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

/* ===== WIDTH AND HEIGHT UTILITIES ===== */
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-fit { width: fit-content; }

.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-fit { height: fit-content; }

.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }

.max-w-none { max-width: none; }
.max-w-xs { max-width: 320px; }
.max-w-sm { max-width: 384px; }
.max-w-md { max-width: 448px; }
.max-w-lg { max-width: 512px; }
.max-w-xl { max-width: 576px; }
.max-w-2xl { max-width: 672px; }
.max-w-3xl { max-width: 768px; }
.max-w-4xl { max-width: 896px; }
.max-w-5xl { max-width: 1024px; }
.max-w-6xl { max-width: 1152px; }
.max-w-full { max-width: 100%; }

/* ===== OVERFLOW UTILITIES ===== */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-scroll { overflow-x: scroll; }

.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-scroll { overflow-y: scroll; }

/* ===== Z-INDEX UTILITIES ===== */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }

/* ===== OPACITY UTILITIES ===== */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* ===== CURSOR UTILITIES ===== */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

/* ===== POINTER EVENTS ===== */
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* ===== USER SELECT ===== */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* ===== BORDER UTILITIES ===== */
.border { border: 1px solid var(--border-color); }
.border-0 { border: 0; }
.border-t { border-top: 1px solid var(--border-color); }
.border-r { border-right: 1px solid var(--border-color); }
.border-b { border-bottom: 1px solid var(--border-color); }
.border-l { border-left: 1px solid var(--border-color); }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }
.rounded-none { border-radius: 0; }

/* ===== SHADOW UTILITIES ===== */
.shadow { box-shadow: var(--card-shadow); }
.shadow-lg { box-shadow: var(--card-shadow-hover); }
.shadow-xl { box-shadow: var(--modal-shadow); }
.shadow-none { box-shadow: none; }

/* ===== BACKGROUND UTILITIES ===== */
.bg-transparent { background-color: transparent; }
.bg-current { background-color: currentColor; }

/* ===== VISIBILITY UTILITIES ===== */
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 768px) {
  .md\:d-none { display: none; }
  .md\:d-block { display: block; }
  .md\:d-flex { display: flex; }
  .md\:flex-col { flex-direction: column; }
  .md\:w-full { width: 100%; }
  .md\:text-center { text-align: center; }
}

@media (max-width: 480px) {
  .sm\:d-none { display: none; }
  .sm\:d-block { display: block; }
  .sm\:d-flex { display: flex; }
  .sm\:flex-col { flex-direction: column; }
  .sm\:w-full { width: 100%; }
  .sm\:text-center { text-align: center; }
  .sm\:p-2 { padding: var(--spacing-sm); }
  .sm\:p-3 { padding: var(--spacing-md); }
}
