/* ===================================
   MAIN CSS FILE - ZDS DATAQUEST
   ================================= */

/* ===== BASE STYLES ===== */
@import './base/_variables.css';
@import './base/_reset.css';
@import './base/_typography.css';
@import './base/_animations.css';

/* ===== LAYOUT STYLES ===== */
@import './layout/_app.css';
@import './layout/_sidebar.css';

/* ===== COMPONENT STYLES ===== */
@import './components/_buttons.css';
@import './components/_forms.css';

/* ===== UTILITY STYLES ===== */
@import './utils/_utilities.css';

/* ===== PAGE STYLES ===== */
@import './pages/_request-report.css';
@import './pages/_dashboard.css';
@import './pages/_users.css';
@import './pages/_profile.css';
@import './pages/_settings.css';

/* ===================================
   TEMPORARY LEGACY IMPORTS
   (To be refactored)
   ================================= */

/* Note: These will be gradually moved to the new structure */
@import './Sidebar.css';
@import './Dashboard.css';
@import './RequestReport.css';
@import './Requests.css';
@import './Users.css';
@import './Profile.css';
@import './Settings.css';
@import './Login.css';
@import './Welcome.css';
@import './Tickets.css';
@import './CreateRequest.css';
@import './RequestDetail.css';
@import './RequestModal.css';
@import './UserDetail.css';
@import './Notifications.css';
@import './Feedback.css';
@import './SendFeedback.css';
@import './ReleaseNotes.css';
@import './EventManagement.css';
@import './AskAI.css';
@import './PlatformSelect.css';
@import './WhitelistPlatform.css';
@import './CustomQuill.css';
@import './BasicQuill.css';

/* ===================================
   GLOBAL OVERRIDES
   ================================= */

/* Ensure consistent box-sizing */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Focus styles for better accessibility */
*:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Remove focus outline for mouse users */
*:focus:not(:focus-visible) {
  outline: none;
}

/* Ensure images are responsive by default */
img {
  max-width: 100%;
  height: auto;
}

/* Better text rendering */
body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===================================
   PRINT STYLES
   ================================= */
@media print {
  /* Hide non-essential elements when printing */
  .sidebar,
  .header-actions,
  .btn,
  .no-print {
    display: none !important;
  }
  
  /* Adjust layout for printing */
  .content {
    margin-left: 0 !important;
    padding: 0 !important;
  }
  
  /* Ensure good contrast for printing */
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  /* Page breaks */
  .page-break {
    page-break-before: always;
  }
  
  .no-page-break {
    page-break-inside: avoid;
  }
}

/* ===================================
   HIGH CONTRAST MODE SUPPORT
   ================================= */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-secondary: #000000;
    --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  [data-theme="dark"] {
    --border-color: #ffffff;
    --text-secondary: #ffffff;
  }
  
  .btn {
    border-width: 2px;
  }
  
  .form-input,
  .form-select,
  .form-textarea {
    border-width: 2px;
  }
}

/* ===================================
   REDUCED MOTION SUPPORT
   ================================= */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===================================
   BROWSER-SPECIFIC FIXES
   ================================= */

/* Firefox */
@-moz-document url-prefix() {
  /* Firefox-specific styles */
  .form-select {
    background-position: right 8px center;
  }
}

/* Safari */
@supports (-webkit-appearance: none) {
  /* Safari-specific styles */
  .form-input,
  .form-select {
    -webkit-appearance: none;
    appearance: none;
  }
}

/* Edge */
@supports (-ms-ime-align: auto) {
  /* Edge-specific styles */
  .grid-container {
    display: -ms-grid;
  }
}

/* ===================================
   DEVELOPMENT HELPERS
   ================================= */

/* Only show in development mode */
.dev-only {
  display: none;
}

/* Debug mode - uncomment to see layout boundaries */
/*
.debug * {
  outline: 1px solid red;
}

.debug .flex-container {
  outline: 2px solid blue;
}

.debug .grid-container {
  outline: 2px solid green;
}
*/

/* ===================================
   PERFORMANCE OPTIMIZATIONS
   ================================= */

/* Use GPU acceleration for animations */
.btn,
.card,
.modal {
  transform: translateZ(0);
  will-change: transform;
}

/* Optimize repaints */
.sidebar,
.content-header {
  contain: layout style paint;
}

/* ===================================
   FUTURE-PROOFING
   ================================= */

/* CSS Grid fallback for older browsers */
@supports not (display: grid) {
  .grid-container {
    display: flex;
    flex-wrap: wrap;
  }
  
  .grid-container > * {
    flex: 1 1 300px;
  }
}

/* Flexbox fallback for even older browsers */
@supports not (display: flex) {
  .flex-container {
    display: block;
  }
  
  .flex-container > * {
    display: inline-block;
    vertical-align: top;
  }
}
