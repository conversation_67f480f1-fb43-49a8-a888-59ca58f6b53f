/* ===================================
   APP LAYOUT STYLES
   ================================= */

/* Main app container */
.app-container {
  display: flex;
  min-height: 100vh;
  position: relative;
  background-color: var(--bg-color);
}

/* Sidebar collapsed state */
.sidebar-collapsed .content {
  margin-left: 80px;
}

/* ===== CONTENT AREA ===== */
.content {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
  transition: margin-left var(--transition-normal), background-color var(--transition-normal);
  margin-left: 280px;
  min-height: 100vh;
}

/* Content header */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-md) var(--spacing-2xl);
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-normal);
}

.content-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-actions {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

/* Content body */
.content-body {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-normal);
  min-height: calc(100vh - 140px);
}

/* ===== PAGE CONTAINERS ===== */
.page-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  gap: var(--spacing-lg);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  color: var(--text-color);
}

.page-actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

/* Back button */
.back-button {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-button:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateX(-2px);
}

/* ===== SECTION CONTAINERS ===== */
.section-container {
  background-color: var(--section-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--card-shadow);
  margin-bottom: var(--spacing-2xl);
  transition: all var(--transition-normal);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.section-actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

/* ===== GRID LAYOUTS ===== */
.grid-container {
  display: grid;
  gap: var(--spacing-xl);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* ===== FLEX LAYOUTS ===== */
.flex-container {
  display: flex;
  gap: var(--spacing-lg);
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-start {
  justify-content: flex-start;
  align-items: center;
}

.flex-end {
  justify-content: flex-end;
  align-items: center;
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .content-header {
  background-color: var(--content-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .content-header h1 {
  color: var(--text-color);
}

[data-theme="dark"] .content-body {
  background-color: var(--content-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .section-container {
  background-color: var(--section-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .section-header {
  border-bottom-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .content {
    margin-left: 0;
    padding: var(--spacing-lg);
  }
  
  .content-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
  }
  
  .page-title {
    order: 1;
    text-align: center;
  }
  
  .back-button {
    order: 0;
    align-self: flex-start;
  }
  
  .page-actions {
    order: 2;
    justify-content: center;
  }
  
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .flex-container {
    flex-direction: column;
  }
  
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .content {
    padding: var(--spacing-md);
  }
  
  .content-header,
  .content-body,
  .section-container {
    padding: var(--spacing-lg);
  }
}
