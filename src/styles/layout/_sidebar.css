/* ===================================
   SIDEBAR LAYOUT STYLES
   ================================= */

/* ===== SIDEBAR CONTAINER ===== */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, var(--content-bg) 0%, var(--content-bg) 100%);
  height: 100vh;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible;
  z-index: var(--z-fixed);
  border-right: 1px solid var(--border-color);
}

.sidebar.collapsed {
  width: 80px;
}

/* ===== COLLAPSE BUTTON ===== */
.collapse-sidebar-fixed-btn {
  position: absolute;
  top: 36px;
  right: -12px;
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  background-color: var(--secondary-purple);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  cursor: pointer;
  z-index: calc(var(--z-fixed) + 1);
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
  transition: all var(--transition-normal);
}

.collapse-sidebar-fixed-btn:hover {
  background-color: #5b4bc9;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
}

.sidebar.collapsed .collapse-sidebar-fixed-btn {
  right: -12px;
  top: 36px;
}

/* ===== SIDEBAR INNER CONTENT ===== */
.sidebar-inner {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* ===== HEADER SECTION ===== */
.sidebar-header {
  padding: var(--spacing-2xl) var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
}

.sidebar.collapsed .sidebar-header {
  flex-direction: column;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl) var(--spacing-md);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  width: 100%;
}

.logo-container:hover {
  background-color: var(--hover-bg);
  transform: translateY(-2px);
}

.sidebar-logo {
  width: 36px;
  height: 36px;
  object-fit: contain;
  transition: all var(--transition-normal);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.sidebar.collapsed .sidebar-logo {
  width: 42px;
  height: 42px;
  margin: 0 auto;
}

.logo {
  font-size: 1.25rem;
  font-weight: 600;
  background: linear-gradient(90deg, var(--primary-green) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  transition: all var(--transition-normal);
}

/* ===== USER INFO SECTION ===== */
.user-info {
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all var(--transition-normal);
  cursor: pointer;
  border-radius: var(--spacing-md);
}

.user-info:hover {
  background-color: var(--hover-bg);
  transform: translateY(-2px);
}

.user-avatar {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--card-shadow);
  flex-shrink: 0;
  transition: all var(--transition-normal);
}

.user-avatar-icon {
  width: 32px;
  height: 32px;
  color: var(--secondary-purple);
  transition: all var(--transition-normal);
}

.user-details {
  flex: 1;
  min-width: 0;
  transition: all var(--transition-normal);
}

.user-details h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-details p {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar.collapsed .user-details {
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.sidebar.collapsed .user-avatar {
  width: 42px;
  height: 42px;
  margin: 0 auto;
}

/* ===== DIVIDER ===== */
.sidebar-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--border-color) 50%, transparent 100%);
  margin: var(--spacing-sm) var(--spacing-xl);
  opacity: 0.6;
}

/* ===== NAVIGATION MENU ===== */
.sidebar-menu {
  flex: 1;
  padding: var(--spacing-md) var(--spacing-md);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
  min-height: 0;
}

.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: var(--radius-sm);
}

.sidebar-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  position: relative;
  margin-bottom: var(--spacing-xs);
  cursor: pointer;
  border-radius: var(--spacing-md);
  transition: all var(--transition-normal);
}

.menu-item-content {
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
  z-index: 1;
}

.sidebar-menu li:hover {
  background-color: var(--hover-bg);
}

.sidebar-menu li.active {
  background: linear-gradient(90deg, rgba(0, 51, 201, 0.08) 0%, rgba(0, 51, 201, 0.03) 100%);
}

.active-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: linear-gradient(180deg, var(--primary-blue) 0%, var(--secondary-purple) 100%);
  border-radius: var(--radius-sm) 0 0 var(--radius-sm);
  transition: all var(--transition-normal);
}

.menu-icon {
  font-size: 1.25rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all var(--transition-normal);
}

.sidebar-menu li.active .menu-icon {
  color: var(--secondary-purple);
}

.menu-label {
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--text-color);
  transition: all var(--transition-normal);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-menu li.active .menu-label {
  color: var(--secondary-purple);
  font-weight: 600;
}

.sidebar.collapsed .menu-label {
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.sidebar.collapsed .sidebar-menu li {
  display: flex;
  justify-content: center;
  padding: var(--spacing-md) 0;
}

.sidebar.collapsed .menu-item-content {
  padding: 0;
  justify-content: center;
  gap: 0;
}

.sidebar.collapsed .active-indicator {
  right: 0;
  width: 4px;
  height: 60%;
}

/* ===== FOOTER SECTION ===== */
.sidebar-footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: auto;
  position: sticky;
  bottom: 0;
  background-color: var(--content-bg);
  z-index: var(--spacing-md);
}

.sidebar.collapsed .sidebar-footer {
  padding: var(--spacing-lg) var(--spacing-md);
  align-items: center;
}

.theme-toggle-btn,
.logout-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: none;
  border: 1px solid transparent;
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: left;
  cursor: pointer;
  border-radius: var(--spacing-md);
  transition: all var(--transition-normal);
  color: var(--text-color);
  width: 100%;
}

.theme-toggle-btn:hover {
  background-color: rgba(111, 12, 223, 0.08);
  border-color: rgba(111, 12, 223, 0.2);
  transform: translateY(-2px);
}

.logout-btn:hover {
  background-color: rgba(244, 67, 54, 0.08);
  border-color: rgba(244, 67, 54, 0.2);
  transform: translateY(-2px);
}

.footer-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.theme-icon {
  font-size: 1.125rem;
  color: var(--secondary-purple);
  transition: all var(--transition-normal);
}

.logout-icon {
  font-size: 1.125rem;
  color: var(--error-color);
  transition: all var(--transition-normal);
}

.footer-text {
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-normal);
}

.sidebar.collapsed .theme-toggle-btn,
.sidebar.collapsed .logout-btn {
  padding: var(--spacing-md);
  justify-content: center;
  width: 48px;
  height: 48px;
}

.sidebar.collapsed .footer-text {
  display: none;
}

.expand-sidebar-btn {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  background-color: var(--secondary-purple);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  cursor: pointer;
  margin-top: var(--spacing-lg);
  transition: all var(--transition-normal);
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
}

.expand-sidebar-btn:hover {
  transform: scale(1.1);
  background-color: #5b4bc9;
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .sidebar {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .collapse-sidebar-fixed-btn {
  background-color: var(--secondary-purple);
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
}

[data-theme="dark"] .collapse-sidebar-fixed-btn:hover {
  background-color: #5b4bc9;
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
}

[data-theme="dark"] .logo-container:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .user-avatar {
  background: linear-gradient(135deg, rgba(0, 51, 201, 0.2) 0%, rgba(0, 0, 0, 0.1) 100%);
}

[data-theme="dark"] .sidebar-menu li:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .expand-sidebar-btn {
  background-color: var(--secondary-purple);
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
}

[data-theme="dark"] .expand-sidebar-btn:hover {
  background-color: #5b4bc9;
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    z-index: var(--z-modal);
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
  }

  .collapse-sidebar-fixed-btn {
    display: none;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100%;
  }

  .sidebar-header {
    padding: var(--spacing-lg);
  }

  .sidebar-menu {
    padding: var(--spacing-sm);
  }

  .menu-item-content {
    padding: var(--spacing-lg);
  }

  .sidebar-footer {
    padding: var(--spacing-lg);
  }
}
