/* ===================================
   HEADER LAYOUT STYLES
   ================================= */

/* ===== MAIN HEADER ===== */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--content-bg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  position: sticky;
  top: 0;
  z-index: var(--z-header);
}

/* ===== HEADER LEFT ===== */
.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.header-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  text-decoration: none;
}

.header-logo-icon {
  font-size: 1.5rem;
  color: var(--primary-blue);
}

/* ===== HEADER CENTER ===== */
.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 600px;
  margin: 0 var(--spacing-xl);
}

.header-search {
  position: relative;
  width: 100%;
}

.header-search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.header-search-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
}

.header-search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 1rem;
}

/* ===== HEADER RIGHT ===== */
.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.header-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.header-action-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

.header-notification-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background-color: var(--error-color);
  border-radius: var(--radius-full);
  border: 2px solid var(--content-bg);
}

/* ===== USER MENU ===== */
.header-user-menu {
  position: relative;
}

.header-user-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--text-color);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.header-user-button:hover {
  background-color: var(--hover-bg);
}

.header-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-purple));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.header-user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.header-user-name {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.2;
}

.header-user-role {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.2;
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .app-header {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .header-search-input {
  background-color: var(--input-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .app-header {
    padding: var(--spacing-sm) var(--spacing-lg);
  }

  .header-center {
    display: none;
  }

  .header-user-info {
    display: none;
  }

  .header-actions {
    gap: var(--spacing-xs);
  }

  .header-action-button {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .header-logo span {
    display: none;
  }

  .header-right {
    gap: var(--spacing-sm);
  }
}
