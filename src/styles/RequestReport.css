.request-report-container {
  width: 100%;
}

.request-report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.back-button {
  flex-shrink: 0;
}

.request-report-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.request-report-actions {
  display: flex;
  gap: 12px;
}

.request-report-section {
  margin-bottom: 32px;
}

.request-report-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.request-report-section-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Tabs */
.request-report-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
}

.tab-buttons {
  display: flex;
}

.tab-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-right: 8px;
}

.tab-actions .btn-primary,
.tab-actions .btn-secondary {
  height: 40px;
}

/* Custom button styles for tab actions */
.tab-actions button[title="Clear All Selections"] {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #595959;
}

.tab-actions button[title="Clear All Selections"]:hover:not(:disabled) {
  background-color: #e8e8e8;
  border-color: #c0c0c0;
  color: #434343;
}

.tab-actions button[title="Clear All Selections"]:disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
}

.tab-actions button[title="Refresh Page"] {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.tab-actions button[title="Refresh Page"]:hover {
  background-color: #bae7ff;
  border-color: #69c0ff;
  color: #0050b3;
}

.tab-actions .btn-primary {
  padding: 0 20px;
}

/* .refresh-button,
.btn-secondary svg {
  padding: 8px;
  min-width: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button svg,
.btn-secondary svg {
  width: 22px;
  height: 22px;
  margin-right: 4px;
} */

.tab-button {
  padding: 12px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.tab-button:hover {
  color: var(--text-color, #333);
  background-color: var(--hover-bg, #f5f5f5);
}

.tab-button.active {
  color: var(--primary-blue, #0033C9);
  border-bottom-color: var(--primary-blue, #0033C9);
}

/* Search and Filter */
.filter-search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 12px;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 200px;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary, #666);
}

.search-icon.spinning {
  animation: spinner 1s linear infinite;
}

.clear-search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary, #666);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
}

.clear-search-icon:hover {
  color: var(--text-color, #333);
  background-color: var(--hover-bg, #f5f5f5);
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 36px;
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-bg, #fff);
  color: var(--text-color, #333);
  flex: 1;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-blue, #0033C9);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.clear-search {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary, #666);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
}

.clear-search:hover {
  background-color: var(--hover-bg, #f5f5f5);
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 4px;
  background-color: var(--input-bg, #fff);
  color: var(--text-color, #333);
  font-size: 14px;
  width: 200px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-blue, #0033C9);
}

.clear-filters-btn {
  background: none;
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  color: var(--text-color, #333);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  flex-shrink: 0;
  height: 38px;
}

.clear-filters-btn:hover {
  background-color: var(--hover-bg, #f5f5f5);
}

/* Infinite Scroll */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  gap: 10px;
  color: var(--text-secondary, #666);
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
}

.loading-spinner-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-left: 8px;
  border: 2px solid #ddd;
  border-top-color: var(--primary-blue, #0033C9);
  border-radius: 50%;
  animation: spinner 0.8s linear infinite;
}

[data-theme="dark"] .loading-spinner-small {
  border-color: #444;
  border-top-color: var(--primary-blue, #0033C9);
}

.scroll-sentinel {
  height: 50px;
  width: 100%;
  margin-top: 20px;
  position: relative;
  z-index: 1;
}

.request-report-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.report-item {
  background-color: var(--section-bg);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
}

.report-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.report-item.selected {
  border-color: var(--primary-blue);
}

.report-item-thumbnail {
  width: 100%;
  height: 150px;
  background-color: #f0f0f0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  overflow: hidden;
}

.report-item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.report-item-thumbnail .placeholder-icon {
  font-size: 48px;
  color: #ccc;
}

.report-item-content {
  padding: 16px;
}

.report-item-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.report-item-owner {
  font-size: 14px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
}

.report-item-updated {
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 6px;
}

.report-item-selection {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: var(--content-bg);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-item-selection.selected {
  background-color: var(--primary-blue);
  color: white;
}



.btn-primary {
  background-color: var(--primary-blue);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #0029a0;
}

.btn-primary:disabled {
  cursor: not-allowed;
  pointer-events: none;
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: var(--hover-bg);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  position: relative;
}

.loading-spinner::after {
  content: "";
  width: 30px;
  height: 30px;
  border: 3px solid #ddd;
  border-top-color: var(--primary-blue, #0033C9);
  border-radius: 50%;
  animation: spinner 0.8s linear infinite;
  position: absolute;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

[data-theme="dark"] .loading-spinner::after {
  border-color: #444;
  border-top-color: var(--primary-blue, #0033C9);
}

.error-message {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

[data-theme="dark"] .error-message {
  background-color: rgba(255, 77, 79, 0.1);
  border-color: rgba(255, 77, 79, 0.3);
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--primary-green, #00CF6A);
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: fadeIn 0.3s ease-in-out, fadeOut 0.3s ease-in-out 2.7s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-10px); }
}

.empty-state {
  text-align: center;
  padding: 40px;
  background-color: var(--section-bg);
  border-radius: 8px;
  color: var(--text-secondary);
}

/* Modal Styles */
.request-report-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.request-report-modal-container {
  background-color: var(--content-bg, white);
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.request-report-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color, #eee);
}

.request-report-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color, #333);
}

.request-report-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary, #666);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.request-report-modal-close:hover {
  background-color: var(--hover-bg, #f5f5f5);
  color: var(--text-color, #333);
}

.request-report-modal-body {
  padding: 24px 24px 4px 24px;
}

.request-report-modal-actions {
  display: flex;
  justify-content: space-between;
  padding: 0 24px 24px;
}

.request-report-modal-actions-left {
  display: flex;
  gap: 12px;
}

.request-report-modal-actions-right {
  display: flex;
  gap: 12px;
}

.selected-items-summary {
  background-color: var(--section-bg, #f9f9f9);
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.selected-items-header {
  margin: 12px 0 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  font-size: 15px;
}

.selected-items-header:first-child {
  margin-top: 0;
}

.selected-items-list {
  margin-bottom: 16px;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 8px;
  margin-bottom: 4px;
  background-color: var(--content-bg, white);
  border-radius: 4px;
  font-size: 14px;
}

.selected-item:hover {
  background-color: var(--hover-bg, #f5f5f5);
}

.selected-item-name {
  font-weight: 500;
  color: var(--text-color, #333);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 12px;
}

.selected-item-owner {
  color: var(--text-secondary, #666);
  font-size: 13px;
}

.no-items-selected {
  color: var(--text-secondary, #666);
  font-style: italic;
  padding: 8px 0;
}

.form-icon {
  margin-right: 6px;
  vertical-align: middle;
  color: var(--text-secondary, #666);
}

/* Dark theme support for modal */
[data-theme="dark"] .request-report-modal-container {
  background-color: var(--section-bg-dark, #2d333b);
}

[data-theme="dark"] .request-report-modal-header {
  border-color: var(--border-color-dark, #444c56);
}

[data-theme="dark"] .request-report-modal-header h3 {
  color: var(--text-color-dark, #e6edf3);
}

[data-theme="dark"] .request-report-modal-close {
  color: var(--text-secondary-dark, #adbac7);
}

[data-theme="dark"] .request-report-modal-close:hover {
  background-color: var(--hover-bg-dark, #444c56);
  color: var(--text-color-dark, #e6edf3);
}

[data-theme="dark"] .selected-items-summary {
  background-color: var(--section-bg-dark-lighter, #373e47);
}

[data-theme="dark"] .selected-items-header {
  border-color: var(--border-color-dark, #444c56);
}

[data-theme="dark"] .selected-item {
  background-color: var(--content-bg-dark, #2d333b);
}

[data-theme="dark"] .selected-item:hover {
  background-color: var(--hover-bg-dark, #444c56);
}

[data-theme="dark"] .selected-item-name {
  color: var(--text-color-dark, #e6edf3);
}

[data-theme="dark"] .selected-item-owner {
  color: var(--text-secondary-dark, #adbac7);
}

[data-theme="dark"] .report-item-updated {
  color: var(--text-secondary-dark, #adbac7);
}

[data-theme="dark"] .no-items-selected {
  color: var(--text-secondary-dark, #adbac7);
}

[data-theme="dark"] .form-icon {
  color: var(--text-secondary-dark, #adbac7);
}

/* Dark mode styles */
[data-theme="dark"] .tab-button {
  color: var(--text-secondary-dark, #aaa);
}

[data-theme="dark"] .tab-button:hover {
  color: var(--text-color-dark, #eee);
  background-color: var(--hover-bg-dark, #333);
}

[data-theme="dark"] .tab-button.active {
  color: var(--primary-blue, #4d79ff);
  border-bottom-color: var(--primary-blue, #4d79ff);
}

/* Dark mode styles for custom buttons */
[data-theme="dark"] .tab-actions button[title="Clear All Selections"] {
  background-color: #262626;
  border-color: #434343;
  color: #d9d9d9;
}

[data-theme="dark"] .tab-actions button[title="Clear All Selections"]:hover:not(:disabled) {
  background-color: #303030;
  border-color: #595959;
  color: #f5f5f5;
}

[data-theme="dark"] .tab-actions button[title="Clear All Selections"]:disabled {
  background-color: #262626;
  border-color: #434343;
  color: #595959;
}

[data-theme="dark"] .tab-actions button[title="Refresh Page"] {
  background-color: #111d2c;
  border-color: #153450;
  color: #40a9ff;
}

[data-theme="dark"] .tab-actions button[title="Refresh Page"]:hover {
  background-color: #112a45;
  border-color: #15395b;
  color: #69c0ff;
}

[data-theme="dark"] .search-input,
[data-theme="dark"] .filter-select {
  background-color: var(--input-bg-dark, #333);
  color: var(--text-color-dark, #eee);
  border-color: var(--border-color-dark, #555);
}

[data-theme="dark"] .search-icon,
[data-theme="dark"] .clear-search,
[data-theme="dark"] .clear-search-icon {
  color: var(--text-secondary-dark, #aaa);
}

[data-theme="dark"] .clear-search-icon:hover {
  color: var(--text-color-dark, #eee);
  background-color: var(--hover-bg-dark, #444);
}

[data-theme="dark"] .clear-filters-btn {
  color: var(--text-color-dark, #eee);
  border-color: var(--border-color-dark, #555);
}

[data-theme="dark"] .clear-filters-btn:hover,
[data-theme="dark"] .clear-search:hover {
  background-color: var(--hover-bg-dark, #444);
}

[data-theme="dark"] .loading-more {
  color: var(--text-secondary-dark, #aaa);
}

/* Success Modal Styles */
.success-modal {
  max-width: 500px;
}

.success-modal-body {
  text-align: center;
  padding: 20px 24px 30px;
}

.success-modal-icon {
  font-size: 60px;
  color: var(--primary-green, #00CF6A);
  margin-bottom: 20px;
}

.success-modal-message {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color, #333);
  margin-bottom: 10px;
}

.success-modal-description {
  font-size: 14px;
  color: var(--text-secondary, #666);
  margin-bottom: 0;
}

.request-details {
  background-color: var(--bg-light, #f5f5f5);
  border-radius: 8px;
  padding: 12px 16px;
  margin: 15px 0;
  text-align: left;
}

.request-details p {
  margin: 6px 0;
  font-size: 14px;
  color: var(--text-color, #333);
}

[data-theme="dark"] .success-modal-icon {
  color: var(--primary-green, #00CF6A);
}

[data-theme="dark"] .success-modal-message {
  color: var(--text-color-dark, #e6edf3);
}

[data-theme="dark"] .success-modal-description {
  color: var(--text-secondary-dark, #adbac7);
}

[data-theme="dark"] .request-details {
  background-color: var(--bg-dark, #2d333b);
  border: 1px solid var(--border-color-dark, #444c56);
}

[data-theme="dark"] .request-details p {
  color: var(--text-color-dark, #e6edf3);
}

/* Responsive styles */
@media (max-width: 768px) {
  .request-report-grid {
    grid-template-columns: 1fr;
  }

  .request-report-header {
    flex-wrap: wrap;
    gap: 12px;
  }

  .request-report-title {
    order: 1;
    width: 100%;
    margin-top: 8px;
  }

  .back-button {
    order: 0;
  }

  .request-report-actions {
    order: 2;
  }

  .btn-primary, .btn-secondary {
    width: 100%;
    justify-content: center;
  }

  .filter-search-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .clear-filters-btn {
    margin-top: 8px;
    width: 100%;
    justify-content: center;
    margin-left: 0;
  }

  .filter-container {
    justify-content: space-between;
  }

  .request-report-tabs {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding-bottom: 16px;
  }

  .tab-buttons {
    overflow-x: auto;
    white-space: nowrap;
  }

  .tab-actions {
    margin-right: 0;
    width: 100%;
    gap: 12px;
  }

  .refresh-button,
  .submit-button {
    flex: 1;
  }
}
