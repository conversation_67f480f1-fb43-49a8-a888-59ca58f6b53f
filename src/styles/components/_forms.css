/* ===================================
   FORM COMPONENTS
   ================================= */

/* ===== FORM CONTAINERS ===== */
.form-container {
  width: 100%;
  max-width: 600px;
}

.form-section {
  margin-bottom: var(--spacing-2xl);
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-header {
  margin-bottom: var(--spacing-xl);
}

.form-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.form-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* ===== FORM GROUPS ===== */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-row {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-start;
}

.form-col {
  flex: 1;
}

.form-col-auto {
  flex: 0 0 auto;
}

/* ===== LABELS ===== */
.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
  line-height: 1.4;
}

.form-label.required::after {
  content: " *";
  color: var(--error-color);
}

.form-label-inline {
  display: inline-block;
  margin-right: var(--spacing-md);
  margin-bottom: 0;
}

/* ===== INPUT FIELDS ===== */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-family: inherit;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all var(--transition-fast);
  line-height: 1.4;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background-color: var(--hover-bg);
  color: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.7;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-muted);
}

/* Input sizes */
.form-input-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.8125rem;
}

.form-input-lg {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: 1rem;
}

/* ===== TEXTAREA ===== */
.form-textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
}

.form-textarea-sm {
  min-height: 80px;
}

.form-textarea-lg {
  min-height: 150px;
}

/* ===== SELECT DROPDOWN ===== */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-md) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
  cursor: pointer;
}

.form-select:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%230033C9' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* ===== CHECKBOX AND RADIO ===== */
.form-check {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.form-check:last-child {
  margin-bottom: 0;
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
  accent-color: var(--primary-blue);
}

.form-check-label {
  font-size: 0.875rem;
  color: var(--text-color);
  cursor: pointer;
  line-height: 1.4;
  margin: 0;
}

.form-check-inline {
  display: inline-flex;
  margin-right: var(--spacing-xl);
  margin-bottom: 0;
}

/* ===== INPUT GROUPS ===== */
.input-group {
  display: flex;
  align-items: stretch;
  width: 100%;
}

.input-group .form-input {
  border-radius: 0;
  border-right: 0;
}

.input-group .form-input:first-child {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.input-group .form-input:last-child {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-right: 1px solid var(--border-color);
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--hover-bg);
  border: 1px solid var(--border-color);
  border-left: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
  white-space: nowrap;
}

.input-group-text:first-child {
  border-left: 1px solid var(--border-color);
  border-right: 0;
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.input-group-text:last-child {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}

/* ===== SEARCH INPUTS ===== */
.search-container {
  position: relative;
  width: 100%;
}

.search-input {
  padding-left: 40px;
  padding-right: 40px;
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

.search-clear {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.search-clear:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

/* ===== FORM VALIDATION ===== */
.form-input.is-valid,
.form-select.is-valid,
.form-textarea.is-valid {
  border-color: var(--success-color);
}

.form-input.is-valid:focus,
.form-select.is-valid:focus,
.form-textarea.is-valid:focus {
  box-shadow: 0 0 0 3px rgba(0, 207, 106, 0.1);
}

.form-input.is-invalid,
.form-select.is-invalid,
.form-textarea.is-invalid {
  border-color: var(--error-color);
}

.form-input.is-invalid:focus,
.form-select.is-invalid:focus,
.form-textarea.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(255, 76, 76, 0.1);
}

/* ===== HELP TEXT AND FEEDBACK ===== */
.form-help {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  line-height: 1.4;
}

.form-feedback {
  font-size: 0.8125rem;
  margin-top: var(--spacing-xs);
  line-height: 1.4;
}

.form-feedback.valid {
  color: var(--success-color);
}

.form-feedback.invalid {
  color: var(--error-color);
}

/* ===== FORM ACTIONS ===== */
.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  align-items: center;
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
}

.form-actions-left {
  justify-content: flex-start;
}

.form-actions-center {
  justify-content: center;
}

.form-actions-between {
  justify-content: space-between;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
  
  .form-actions {
    flex-direction: column-reverse;
    align-items: stretch;
  }
  
  .form-actions .btn {
    width: 100%;
    justify-content: center;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .input-group .form-input,
  .input-group-text {
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
  }
  
  .input-group .form-input:not(:last-child) {
    margin-bottom: var(--spacing-sm);
  }
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23adbac7' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

[data-theme="dark"] .form-select:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%230033C9' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}
