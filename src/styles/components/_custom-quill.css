/* ===================================
   CUSTOM QUILL EDITOR COMPONENT STYLES
   ================================= */

/* ===== WRAPPER AND LABEL ===== */
.quill-editor-wrapper {
  margin-bottom: var(--spacing-xl);
}

.quill-editor-label {
  display: block;
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.required-mark {
  color: var(--error-color);
  margin-left: var(--spacing-xs);
}

/* ===== MAIN CONTAINER ===== */
.custom-quill-editor {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-family: inherit;
  background-color: var(--input-bg);
  transition: all var(--transition-fast);
  overflow: hidden;
}

.custom-quill-editor:focus-within {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(0, 207, 106, 0.1);
}

/* ===== TOOLBAR STYLES ===== */
.custom-quill-editor .ql-toolbar {
  border: none;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
  border-top-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
  padding: var(--spacing-md);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.custom-quill-editor .ql-toolbar .ql-formats {
  margin-right: var(--spacing-md);
}

.custom-quill-editor .ql-toolbar .ql-formats:last-child {
  margin-right: 0;
}

/* ===== TOOLBAR BUTTONS ===== */
.custom-quill-editor .ql-toolbar button {
  width: 28px;
  height: 28px;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
}

.custom-quill-editor .ql-toolbar button:hover {
  background-color: var(--hover-bg);
  color: var(--primary-green);
}

.custom-quill-editor .ql-toolbar button.ql-active {
  background-color: var(--primary-green);
  color: white;
}

.custom-quill-editor .ql-toolbar button svg {
  width: 16px;
  height: 16px;
}

/* ===== DROPDOWN PICKERS ===== */
.custom-quill-editor .ql-toolbar .ql-picker {
  color: var(--text-color);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.custom-quill-editor .ql-toolbar .ql-picker:hover {
  background-color: var(--hover-bg);
}

.custom-quill-editor .ql-toolbar .ql-picker-label {
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
}

.custom-quill-editor .ql-toolbar .ql-picker-options {
  background-color: var(--content-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--card-shadow);
  z-index: var(--z-dropdown);
  margin-top: var(--spacing-xs);
}

.custom-quill-editor .ql-toolbar .ql-picker-item {
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-color);
  transition: background-color var(--transition-fast);
}

.custom-quill-editor .ql-toolbar .ql-picker-item:hover {
  background-color: var(--hover-bg);
}

/* ===== COLOR PICKER ===== */
.custom-quill-editor .ql-toolbar .ql-color-picker .ql-picker-item {
  border-radius: var(--radius-sm);
  margin: 2px;
  width: 20px;
  height: 20px;
}

/* ===== EDITOR CONTAINER ===== */
.custom-quill-editor .ql-container {
  border: none;
  font-size: 1rem;
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  background-color: var(--input-bg);
}

/* ===== EDITOR CONTENT AREA ===== */
.custom-quill-editor .ql-editor {
  padding: var(--spacing-lg);
  min-height: 150px;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--input-bg);
}

.custom-quill-editor .ql-editor.ql-blank::before {
  font-style: italic;
  color: var(--text-muted);
  left: var(--spacing-lg);
  right: var(--spacing-lg);
}

/* ===== CONTENT FORMATTING ===== */
.custom-quill-editor .ql-editor h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  color: var(--text-color);
}

.custom-quill-editor .ql-editor h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.custom-quill-editor .ql-editor h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.custom-quill-editor .ql-editor p {
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.custom-quill-editor .ql-editor ul,
.custom-quill-editor .ql-editor ol {
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
}

.custom-quill-editor .ql-editor li {
  margin-bottom: var(--spacing-xs);
}

.custom-quill-editor .ql-editor blockquote {
  border-left: 4px solid var(--primary-green);
  padding-left: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  font-style: italic;
  color: var(--text-secondary);
  background-color: var(--hover-bg);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.custom-quill-editor .ql-editor a {
  color: var(--primary-blue);
  text-decoration: underline;
  transition: color var(--transition-fast);
}

.custom-quill-editor .ql-editor a:hover {
  color: var(--primary-blue-dark);
}

.custom-quill-editor .ql-editor strong {
  font-weight: 600;
}

.custom-quill-editor .ql-editor em {
  font-style: italic;
}

.custom-quill-editor .ql-editor code {
  background-color: var(--hover-bg);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--error-color);
}

.custom-quill-editor .ql-editor pre {
  background-color: var(--section-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

/* ===== ADVANCED EDITOR VARIANT ===== */
.advanced-quill-wrapper {
  margin-bottom: var(--spacing-2xl);
}

.advanced-editor {
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.advanced-editor .ql-toolbar {
  background: linear-gradient(135deg, var(--section-bg), var(--hover-bg));
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.advanced-editor .ql-container {
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.advanced-editor .ql-editor {
  min-height: 200px;
  font-size: 1rem;
  line-height: 1.6;
  padding: var(--spacing-xl);
}

/* ===== EDITOR INFO ===== */
.editor-info {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--info-bg);
  border: 1px solid var(--info-color);
  border-radius: var(--radius-sm);
  font-size: 0.8125rem;
  color: var(--info-text);
}

.editor-info p {
  margin: var(--spacing-xs) 0;
}

.editor-info p:first-child {
  margin-top: 0;
}

.editor-info p:last-child {
  margin-bottom: 0;
}

/* ===== CUSTOM THEME ===== */
.custom-theme-toolbar {
  border-color: var(--primary-green) !important;
}

.custom-theme-editor {
  font-family: var(--font-family);
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .custom-quill-editor .ql-toolbar {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .custom-quill-editor .ql-toolbar .ql-picker-options {
  background-color: var(--section-bg);
  border-color: var(--border-color);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .advanced-editor .ql-toolbar {
  background: linear-gradient(135deg, var(--content-bg), var(--section-bg));
}

[data-theme="dark"] .editor-info {
  background-color: var(--content-bg);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .custom-quill-editor .ql-toolbar {
    padding: var(--spacing-sm);
    gap: var(--spacing-xs);
  }

  .custom-quill-editor .ql-toolbar .ql-formats {
    margin-right: var(--spacing-sm);
  }

  .custom-quill-editor .ql-toolbar button {
    width: 32px;
    height: 32px;
  }

  .advanced-editor .ql-toolbar {
    padding: var(--spacing-md);
  }

  .advanced-editor .ql-editor {
    min-height: 150px;
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .custom-quill-editor .ql-editor {
    padding: var(--spacing-md);
    min-height: 120px;
  }

  .custom-quill-editor .ql-toolbar {
    flex-direction: column;
    align-items: flex-start;
  }

  .custom-quill-editor .ql-toolbar .ql-formats {
    margin-bottom: var(--spacing-xs);
  }
}
