/* ===================================
   BASIC QUILL EDITOR COMPONENT STYLES
   ================================= */

/* ===== MAIN CONTAINER ===== */
.basic-quill-editor {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xl);
  background-color: var(--input-bg);
  transition: all var(--transition-fast);
  overflow: hidden;
}

.basic-quill-editor:focus-within {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 2px rgba(0, 207, 106, 0.1);
}

/* ===== TOOLBAR STYLES ===== */
.basic-quill-editor .ql-toolbar {
  border: none;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
  border-top-left-radius: var(--radius-sm);
  border-top-right-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

/* ===== TOOLBAR BUTTONS ===== */
.basic-quill-editor .ql-toolbar button {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-xs);
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
}

.basic-quill-editor .ql-toolbar button:hover {
  background-color: var(--hover-bg);
  color: var(--primary-green);
}

.basic-quill-editor .ql-toolbar button.ql-active {
  background-color: var(--primary-green);
  color: white;
}

.basic-quill-editor .ql-toolbar button svg {
  width: 14px;
  height: 14px;
}

/* ===== DROPDOWN PICKERS ===== */
.basic-quill-editor .ql-toolbar .ql-picker {
  color: var(--text-color);
  border-radius: var(--radius-xs);
  transition: all var(--transition-fast);
}

.basic-quill-editor .ql-toolbar .ql-picker:hover {
  background-color: var(--hover-bg);
}

.basic-quill-editor .ql-toolbar .ql-picker-label {
  border: none;
  padding: var(--spacing-xs);
  border-radius: var(--radius-xs);
  font-size: 0.8125rem;
}

.basic-quill-editor .ql-toolbar .ql-picker-options {
  background-color: var(--content-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  box-shadow: var(--card-shadow);
  z-index: var(--z-dropdown);
  margin-top: var(--spacing-xs);
}

.basic-quill-editor .ql-toolbar .ql-picker-item {
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--text-color);
  transition: background-color var(--transition-fast);
}

.basic-quill-editor .ql-toolbar .ql-picker-item:hover {
  background-color: var(--hover-bg);
}

/* ===== COLOR PICKER ===== */
.basic-quill-editor .ql-toolbar .ql-color-picker .ql-picker-item {
  border-radius: var(--radius-xs);
  margin: 1px;
  width: 16px;
  height: 16px;
}

/* ===== EDITOR CONTAINER ===== */
.basic-quill-editor .ql-container {
  border: none;
  font-size: 1rem;
  min-height: 150px;
  border-bottom-left-radius: var(--radius-sm);
  border-bottom-right-radius: var(--radius-sm);
  background-color: var(--input-bg);
}

/* ===== EDITOR CONTENT AREA ===== */
.basic-quill-editor .ql-editor {
  min-height: 180px;
  padding: var(--spacing-md) var(--spacing-lg);
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--input-bg);
}

.basic-quill-editor .ql-editor.ql-blank::before {
  font-style: italic;
  color: var(--text-muted);
  left: var(--spacing-lg);
  right: var(--spacing-lg);
}

/* ===== CONTENT FORMATTING ===== */
.basic-quill-editor .ql-editor h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.basic-quill-editor .ql-editor h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.basic-quill-editor .ql-editor h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.basic-quill-editor .ql-editor p {
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.basic-quill-editor .ql-editor ul,
.basic-quill-editor .ql-editor ol {
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
}

.basic-quill-editor .ql-editor li {
  margin-bottom: var(--spacing-xs);
}

.basic-quill-editor .ql-editor blockquote {
  border-left: 3px solid var(--primary-green);
  padding-left: var(--spacing-md);
  margin: var(--spacing-md) 0;
  font-style: italic;
  color: var(--text-secondary);
}

.basic-quill-editor .ql-editor a {
  color: var(--primary-blue);
  text-decoration: underline;
  transition: color var(--transition-fast);
}

.basic-quill-editor .ql-editor a:hover {
  color: var(--primary-blue-dark);
}

.basic-quill-editor .ql-editor strong {
  font-weight: 600;
}

.basic-quill-editor .ql-editor em {
  font-style: italic;
}

.basic-quill-editor .ql-editor code {
  background-color: var(--hover-bg);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-xs);
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--error-color);
}

.basic-quill-editor .ql-editor pre {
  background-color: var(--section-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  margin: var(--spacing-md) 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .basic-quill-editor {
  background-color: var(--input-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar button {
  color: var(--text-secondary);
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar button:hover {
  background-color: var(--hover-bg);
  color: var(--primary-green);
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar button.ql-active {
  background-color: var(--primary-green);
  color: white;
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar .ql-picker {
  color: var(--text-color);
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar .ql-picker:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar .ql-picker-options {
  background-color: var(--section-bg);
  border-color: var(--border-color);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar .ql-picker-item {
  color: var(--text-color);
}

[data-theme="dark"] .basic-quill-editor .ql-toolbar .ql-picker-item:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .basic-quill-editor .ql-container {
  background-color: var(--input-bg);
}

[data-theme="dark"] .basic-quill-editor .ql-editor {
  background-color: var(--input-bg);
  color: var(--text-color);
}

[data-theme="dark"] .basic-quill-editor .ql-editor.ql-blank::before {
  color: var(--text-muted);
}

[data-theme="dark"] .basic-quill-editor .ql-editor a {
  color: var(--secondary-light-blue);
}

[data-theme="dark"] .basic-quill-editor .ql-editor a:hover {
  color: var(--primary-blue);
}

[data-theme="dark"] .basic-quill-editor .ql-editor blockquote {
  color: var(--text-secondary);
}

[data-theme="dark"] .basic-quill-editor .ql-editor code {
  background-color: var(--section-bg);
  color: var(--error-color);
}

[data-theme="dark"] .basic-quill-editor .ql-editor pre {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .basic-quill-editor .ql-toolbar {
    padding: var(--spacing-xs);
    gap: 2px;
  }

  .basic-quill-editor .ql-toolbar button {
    width: 28px;
    height: 28px;
  }

  .basic-quill-editor .ql-toolbar button svg {
    width: 16px;
    height: 16px;
  }

  .basic-quill-editor .ql-editor {
    min-height: 150px;
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .basic-quill-editor .ql-editor {
    min-height: 120px;
    padding: var(--spacing-sm);
  }

  .basic-quill-editor .ql-toolbar {
    flex-direction: column;
    align-items: flex-start;
  }

  .basic-quill-editor .ql-toolbar .ql-formats {
    margin-bottom: var(--spacing-xs);
  }
}
