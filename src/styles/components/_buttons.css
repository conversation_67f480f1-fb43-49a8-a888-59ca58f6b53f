/* ===================================
   BUTTON COMPONENTS
   ================================= */

/* ===== BASE BUTTON STYLES ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* ===== BUTTON VARIANTS ===== */

/* Primary button */
.btn-primary {
  background-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
  border-color: var(--btn-primary-bg);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--btn-primary-hover);
  border-color: var(--btn-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 51, 201, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 51, 201, 0.3);
}

/* Secondary button */
.btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--btn-secondary-hover);
  border-color: var(--text-secondary);
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
}

/* Success button */
.btn-success {
  background-color: var(--btn-success-bg);
  color: var(--btn-success-text);
  border-color: var(--btn-success-bg);
}

.btn-success:hover:not(:disabled) {
  background-color: var(--btn-success-hover);
  border-color: var(--btn-success-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 207, 106, 0.3);
}

.btn-success:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 207, 106, 0.3);
}

/* Danger button */
.btn-danger {
  background-color: var(--btn-danger-bg);
  color: var(--btn-danger-text);
  border-color: var(--btn-danger-bg);
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--btn-danger-hover);
  border-color: var(--btn-danger-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 76, 76, 0.3);
}

.btn-danger:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(255, 76, 76, 0.3);
}

/* Outline variants */
.btn-outline-primary {
  background-color: transparent;
  color: var(--primary-blue);
  border-color: var(--primary-blue);
}

.btn-outline-primary:hover:not(:disabled) {
  background-color: var(--primary-blue);
  color: white;
}

.btn-outline-secondary {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.btn-outline-secondary:hover:not(:disabled) {
  background-color: var(--text-secondary);
  color: white;
  border-color: var(--text-secondary);
}

/* Ghost variants */
.btn-ghost {
  background-color: transparent;
  color: var(--text-color);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--hover-bg);
}

.btn-ghost-primary {
  background-color: transparent;
  color: var(--primary-blue);
  border-color: transparent;
}

.btn-ghost-primary:hover:not(:disabled) {
  background-color: var(--filter-option-bg);
}

/* ===== BUTTON SIZES ===== */
.btn-xs {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.8125rem;
}

.btn-lg {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: 1rem;
}

.btn-xl {
  padding: var(--spacing-xl) var(--spacing-2xl);
  font-size: 1.125rem;
}

/* ===== BUTTON SHAPES ===== */
.btn-rounded {
  border-radius: var(--radius-xl);
}

.btn-pill {
  border-radius: 50px;
}

.btn-square {
  border-radius: 0;
}

/* ===== ICON BUTTONS ===== */
.btn-icon {
  padding: var(--spacing-md);
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
}

.btn-icon.btn-sm {
  padding: var(--spacing-sm);
  width: 32px;
  height: 32px;
}

.btn-icon.btn-lg {
  padding: var(--spacing-lg);
  width: 48px;
  height: 48px;
}

.btn-icon-round {
  border-radius: var(--radius-full);
}

/* ===== BUTTON GROUPS ===== */
.btn-group {
  display: inline-flex;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-right-width: 1px;
}

.btn-group .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}

/* ===== LOADING STATES ===== */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spinner 0.8s linear infinite;
}

.btn-loading.btn-primary::after {
  border-color: white;
  border-top-color: transparent;
}

/* ===== SPECIAL BUTTONS ===== */
.btn-floating {
  position: fixed;
  bottom: var(--spacing-2xl);
  right: var(--spacing-2xl);
  width: 56px;
  height: 56px;
  border-radius: var(--radius-full);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: var(--z-fixed);
}

.btn-floating:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

/* Toggle button */
.btn-toggle {
  position: relative;
}

.btn-toggle.active {
  background-color: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.875rem;
  }
  
  .btn-group {
    flex-direction: column;
  }
  
  .btn-group .btn {
    border-right-width: 1px;
    border-bottom-width: 0;
  }
  
  .btn-group .btn:first-child {
    border-radius: var(--radius-md) var(--radius-md) 0 0;
  }
  
  .btn-group .btn:last-child {
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    border-bottom-width: 1px;
  }
  
  .btn-floating {
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 48px;
    height: 48px;
  }
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .btn-icon:hover {
  background-color: var(--hover-bg);
}
