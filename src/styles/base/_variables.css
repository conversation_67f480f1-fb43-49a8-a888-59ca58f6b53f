/* ===================================
   CSS VARIABLES - CONSOLIDATED
   ================================= */

/* ===== PRIMARY COLORS ===== */
:root {
  /* Brand Colors */
  --primary-green: #00CF6A;
  --primary-blue: #0033C9;
  --primary-blue-dark: #002aa5;
  --primary-blue-light: #335FD1;
  --primary-green-dark: #00B85E;

  /* Secondary Colors */
  --secondary-light-blue: #00B4FF;
  --secondary-lime-green: #A1FF00;
  --secondary-purple: #6F0CDF;
  --secondary-yellow: #FFD729;
  --secondary-orange: #FF8A00;

  /* Status Colors */
  --success-color: var(--primary-green);
  --success-color-dark: var(--primary-green-dark);
  --error-color: #FF4C4C;
  --error-color-dark: #E53E3E;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;

  /* ===== BACKGROUND COLORS ===== */
  --bg-color: #f5f7fb;
  --content-bg: #ffffff;
  --section-bg: #ffffff;
  --hover-bg: #f5f7fb;
  --connection-bg: #f8f9fa;
  --filter-bg: #f8f9fa;
  --bg-light: #f8f9fa;
  --hover-color: #f0f0f0;

  /* ===== TEXT COLORS ===== */
  --text-color: #333333;
  --text-secondary: #666666;
  --text-muted: #888888;

  /* ===== BORDER AND INPUT COLORS ===== */
  --border-color: #e0e0e0;
  --input-bg: #ffffff;
  --input-border: #ddd;

  /* ===== STATUS BACKGROUNDS ===== */
  --success-bg: #e7f7ed;
  --success-text: var(--primary-green);
  --error-bg: #ffeeee;
  --error-text: #d63031;
  --warning-bg: rgba(245, 158, 11, 0.1);
  --warning-text: var(--warning-color);
  --info-bg: rgba(59, 130, 246, 0.1);
  --info-text: var(--info-color);

  /* ===== BUTTON COLORS ===== */
  --btn-primary-bg: var(--primary-blue);
  --btn-primary-hover: var(--primary-blue-dark);
  --btn-primary-text: #ffffff;
  
  --btn-secondary-bg: #f5f5f5;
  --btn-secondary-hover: #e0e0e0;
  --btn-secondary-text: #333333;
  
  --btn-success-bg: var(--primary-green);
  --btn-success-hover: var(--primary-green-dark);
  --btn-success-text: #ffffff;
  
  --btn-danger-bg: var(--error-color);
  --btn-danger-hover: var(--error-color-dark);
  --btn-danger-text: #ffffff;

  /* ===== SHADOWS AND EFFECTS ===== */
  --card-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  --card-shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.1);
  --modal-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);

  /* ===== FILTER OPTIONS ===== */
  --filter-option-bg: rgba(0, 51, 201, 0.1);
  --filter-option-text: var(--primary-blue);

  /* ===== SPACING ===== */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;

  /* ===== BORDER RADIUS ===== */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;

  /* ===== TRANSITIONS ===== */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* ===== Z-INDEX LAYERS ===== */
  --z-dropdown: 100;
  --z-sticky: 200;
  --z-fixed: 300;
  --z-modal-backdrop: 1000;
  --z-modal: 1001;
  --z-popover: 1002;
  --z-tooltip: 1003;
}

/* ===================================
   DARK THEME VARIABLES
   ================================= */
[data-theme="dark"] {
  /* Brand Colors - Keep same */
  --primary-green: #00CF6A;
  --primary-blue: #0033C9;
  --primary-blue-dark: #0040f0;
  --primary-blue-light: #4d79ff;

  /* Secondary Colors - Keep same */
  --secondary-light-blue: #00B4FF;
  --secondary-lime-green: #A1FF00;
  --secondary-purple: #6F0CDF;
  --secondary-yellow: #FFD729;
  --secondary-orange: #FF8A00;

  /* Status Colors */
  --error-color: #ff5252;
  --error-color-dark: #d32f2f;

  /* Background Colors */
  --bg-color: #1a1a1a;
  --content-bg: #111827;
  --section-bg: #1f2937;
  --hover-bg: #22272e;
  --connection-bg: #22272e;
  --filter-bg: #22272e;
  --bg-light: #2d2d2d;
  --hover-color: #374151;

  /* Text Colors */
  --text-color: #e6edf3;
  --text-secondary: #adbac7;
  --text-muted: #768390;

  /* Border and Input Colors */
  --border-color: #444c56;
  --input-bg: #111827;
  --input-border: #444c56;

  /* Status Backgrounds */
  --success-bg: rgba(0, 207, 106, 0.15);
  --success-text: var(--primary-green);
  --error-bg: #3a1e1e;
  --error-text: var(--error-color);
  --warning-bg: rgba(245, 158, 11, 0.15);
  --info-bg: rgba(59, 130, 246, 0.15);

  /* Button Colors */
  --btn-secondary-bg: #333333;
  --btn-secondary-hover: #444444;
  --btn-secondary-text: #e6edf3;

  /* Shadows and Effects */
  --card-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  --card-shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.3);
  --modal-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);

  /* Filter Options */
  --filter-option-bg: rgba(0, 51, 201, 0.2);
  --filter-option-text: #6b8aff;
}

/* ===================================
   UTILITY COLOR CLASSES
   ================================= */
.color-light-blue { color: var(--secondary-light-blue); }
.color-lime-green { color: var(--secondary-lime-green); }
.color-purple { color: var(--secondary-purple); }
.color-yellow { color: var(--secondary-yellow); }
.color-orange { color: var(--secondary-orange); }

.bg-light-blue { background-color: var(--secondary-light-blue); }
.bg-lime-green { background-color: var(--secondary-lime-green); }
.bg-purple { background-color: var(--secondary-purple); }
.bg-yellow { background-color: var(--secondary-yellow); }
.bg-orange { background-color: var(--secondary-orange); }
