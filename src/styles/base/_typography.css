/* ===================================
   TYPOGRAPHY STYLES
   ================================= */

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  font-size: 2rem;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1.125rem;
}

h6 {
  font-size: 1rem;
}

/* Paragraphs */
p {
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
  color: var(--text-color);
}

/* Text utilities */
.text-primary { color: var(--primary-blue); }
.text-success { color: var(--success-color); }
.text-error { color: var(--error-color); }
.text-warning { color: var(--warning-color); }
.text-info { color: var(--info-color); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

/* Font weights */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Font sizes */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }

/* Text alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Text transform */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }

/* Text decoration */
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

/* Line height */
.leading-tight { line-height: 1.25; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.75; }

/* Text overflow */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Links */
a {
  color: var(--primary-blue);
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-blue-dark);
}

a:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Lists */
ul, ol {
  margin-bottom: var(--spacing-lg);
  padding-left: var(--spacing-xl);
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

li {
  margin-bottom: var(--spacing-xs);
  color: var(--text-color);
}

/* Blockquotes */
blockquote {
  border-left: 4px solid var(--primary-blue);
  padding-left: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
  font-style: italic;
  color: var(--text-secondary);
}

/* Small text */
small {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Strong and emphasis */
strong, b {
  font-weight: 600;
  color: var(--text-color);
}

em, i {
  font-style: italic;
}

/* Mark/highlight */
mark {
  background-color: var(--secondary-yellow);
  color: var(--text-color);
  padding: 2px 4px;
  border-radius: var(--radius-sm);
}

/* Abbreviations */
abbr[title] {
  border-bottom: 1px dotted var(--text-secondary);
  cursor: help;
  text-decoration: none;
}

/* Responsive typography */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.375rem; }
  h4 { font-size: 1.125rem; }
  h5 { font-size: 1rem; }
  h6 { font-size: 0.875rem; }
}

@media (max-width: 480px) {
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
}
