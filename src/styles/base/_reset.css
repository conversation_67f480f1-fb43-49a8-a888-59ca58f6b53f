/* ===================================
   CSS RESET AND BASE STYLES
   ================================= */

/* Modern CSS Reset */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Remove default styling */
ul,
ol {
  list-style: none;
}

button {
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
}

input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
}

a {
  text-decoration: none;
  color: inherit;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Base HTML elements */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Roboto', 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-secondary);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) var(--bg-color);
}

/* Selection styling */
::selection {
  background-color: var(--primary-blue);
  color: white;
}

::-moz-selection {
  background-color: var(--primary-blue);
  color: white;
}

/* Code elements */
code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
  background-color: var(--hover-bg);
  padding: 2px 4px;
  border-radius: var(--radius-sm);
  font-size: 0.9em;
}

pre {
  background-color: var(--hover-bg);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  overflow-x: auto;
}

pre code {
  background: none;
  padding: 0;
}

/* Loading screen */
.loading-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 18px;
  font-weight: 500;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
