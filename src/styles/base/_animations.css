/* ===================================
   ANIMATIONS AND TRANSITIONS
   ================================= */

/* ===== KEYFRAME ANIMATIONS ===== */

/* Spinner animation */
@keyframes spinner {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade out animation */
@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* Modal fade in */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from left */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide in from right */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Scale in animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Bounce animation */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Pulse animation */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Shake animation */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* ===== ANIMATION UTILITY CLASSES ===== */

/* Loading spinners */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-blue);
  border-radius: 50%;
  animation: spinner 0.8s linear infinite;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.loading-spinner.large {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

/* Fade animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.modal-fade-in {
  animation: modalFadeIn 0.3s ease;
}

/* Slide animations */
.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

/* Scale animations */
.scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Interactive animations */
.bounce {
  animation: bounce 1s ease-in-out;
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

.shake {
  animation: shake 0.5s ease-in-out;
}

/* ===== TRANSITION UTILITIES ===== */

/* Basic transitions */
.transition {
  transition: all var(--transition-fast);
}

.transition-colors {
  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
}

.transition-opacity {
  transition: opacity var(--transition-fast);
}

.transition-transform {
  transition: transform var(--transition-fast);
}

.transition-shadow {
  transition: box-shadow var(--transition-fast);
}

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-fade:hover {
  opacity: 0.8;
}

/* Focus effects */
.focus-ring:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

.focus-ring-inset:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: -2px;
}

/* ===== LOADING STATES ===== */

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

[data-theme="dark"] .loading-overlay {
  background-color: rgba(0, 0, 0, 0.8);
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background-color: var(--primary-blue);
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .loading-spinner {
    animation: none;
    border-top-color: transparent;
  }
}
