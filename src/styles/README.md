# CSS Architecture - ZDS DataQuest

## 📁 Cấu trúc thư mục mới

```
src/styles/
├── base/                    # Base styles và foundations
│   ├── _variables.css       # CSS Variables (consolidated)
│   ├── _reset.css          # CSS Reset và base HTML elements
│   ├── _typography.css     # Typography styles
│   └── _animations.css     # Keyframes và animations
├── layout/                  # Layout components
│   ├── _app.css            # App container và main layout
│   └── _sidebar.css        # Sidebar layout styles
├── components/              # Reusable components
│   ├── _buttons.css        # Button variants và states
│   ├── _forms.css          # Form elements và validation
│   ├── _modals.css         # Modal components (TODO)
│   ├── _tables.css         # Table styles (TODO)
│   └── _cards.css          # Card components (TODO)
├── pages/                   # Page-specific styles
│   ├── _request-report.css # Request Report page (COMPLETED)
│   ├── _dashboard.css      # Dashboard page (TODO)
│   ├── _users.css          # Users page (TODO)
│   └── ...                 # Other pages
├── utils/                   # Utility classes
│   ├── _utilities.css      # Spacing, display, flex utilities
│   └── _responsive.css     # Responsive helpers (TODO)
├── main.css                # Main import file
└── README.md               # This file
```

## 🎯 Mục tiêu đã đạt được

### ✅ Phase 1: Cấu trúc cơ bản
- [x] Tạo cấu trúc thư mục có tổ chức
- [x] Consolidate CSS variables từ nhiều file thành 1 file duy nhất
- [x] Tạo base styles (reset, typography, animations)
- [x] Tạo layout styles (app, sidebar)
- [x] Tạo component styles (buttons, forms)
- [x] Tạo utility classes

### ✅ Phase 2: Refactor RequestReport
- [x] Tách RequestReport.css thành file riêng với naming convention rõ ràng
- [x] Sử dụng CSS variables thay vì hardcode values
- [x] Tối ưu hóa dark theme styles
- [x] Cải thiện responsive design
- [x] Loại bỏ CSS dư thừa

### ✅ Phase 3: Refactor Sidebar
- [x] Tách Sidebar styles thành file layout riêng
- [x] Sử dụng CSS variables
- [x] Cải thiện responsive design

## 🔧 Cách sử dụng

### Import CSS
Chỉ cần import file `main.css` trong `App.js`:
```javascript
import './styles/main.css';
```

### CSS Variables
Tất cả variables được định nghĩa trong `base/_variables.css`:
```css
/* Sử dụng variables */
.my-component {
  background-color: var(--primary-blue);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}
```

### Naming Convention
- **Components**: Prefix với tên component (vd: `request-report-`, `sidebar-`)
- **Utilities**: Tên ngắn gọn (vd: `d-flex`, `mt-4`, `btn-primary`)
- **Variables**: Semantic naming (vd: `--primary-blue`, `--spacing-lg`)

### Responsive Design
Sử dụng mobile-first approach:
```css
/* Mobile first */
.component {
  padding: var(--spacing-md);
}

/* Tablet và desktop */
@media (min-width: 768px) {
  .component {
    padding: var(--spacing-xl);
  }
}
```

## 📋 TODO - Các file cần refactor

### 🔄 Đang chờ refactor
- [ ] Dashboard.css → pages/_dashboard.css
- [ ] Users.css → pages/_users.css
- [ ] Profile.css → pages/_profile.css
- [ ] Settings.css → pages/_settings.css
- [ ] Login.css → pages/_login.css
- [ ] Welcome.css → pages/_welcome.css
- [ ] Tickets.css → pages/_tickets.css
- [ ] CreateRequest.css → pages/_create-request.css
- [ ] RequestDetail.css → pages/_request-detail.css
- [ ] Notifications.css → pages/_notifications.css
- [ ] Feedback.css → pages/_feedback.css
- [ ] ReleaseNotes.css → pages/_release-notes.css
- [ ] EventManagement.css → pages/_event-management.css

### 🆕 Components cần tạo
- [ ] components/_modals.css
- [ ] components/_tables.css
- [ ] components/_cards.css
- [ ] components/_tabs.css
- [ ] components/_dropdowns.css
- [ ] components/_alerts.css

### 🎨 Themes
- [ ] themes/_light.css
- [ ] themes/_dark.css
- [ ] themes/_high-contrast.css

## 🚀 Lợi ích của cấu trúc mới

### 1. **Maintainability**
- CSS được tổ chức theo chức năng rõ ràng
- Dễ tìm và sửa styles
- Tránh duplicate code

### 2. **Performance**
- CSS variables giúp browser optimize
- Tree-shaking unused styles
- Smaller bundle size

### 3. **Developer Experience**
- Naming convention nhất quán
- IntelliSense support tốt hơn
- Dễ debug và test

### 4. **Scalability**
- Dễ thêm components mới
- Consistent design system
- Reusable utilities

## 🎨 Design System

### Colors
```css
/* Primary Colors */
--primary-green: #00CF6A
--primary-blue: #0033C9

/* Secondary Colors */
--secondary-purple: #6F0CDF
--secondary-yellow: #FFD729
```

### Spacing Scale
```css
--spacing-xs: 4px    /* 0.25rem */
--spacing-sm: 8px    /* 0.5rem */
--spacing-md: 12px   /* 0.75rem */
--spacing-lg: 16px   /* 1rem */
--spacing-xl: 20px   /* 1.25rem */
--spacing-2xl: 24px  /* 1.5rem */
--spacing-3xl: 32px  /* 2rem */
```

### Border Radius
```css
--radius-sm: 4px
--radius-md: 8px
--radius-lg: 12px
--radius-xl: 16px
--radius-full: 50%
```

## 📱 Responsive Breakpoints

```css
/* Mobile first approach */
/* xs: 0px - 479px (default) */
/* sm: 480px - 767px */
@media (max-width: 480px) { }

/* md: 768px - 1199px */
@media (max-width: 768px) { }

/* lg: 1200px+ */
@media (min-width: 1200px) { }
```

## 🔍 Migration Guide

### Từ file cũ sang file mới:
1. **Tìm component styles** trong file cũ
2. **Copy styles** sang file mới trong thư mục phù hợp
3. **Thay hardcode values** bằng CSS variables
4. **Thêm prefix** cho class names
5. **Optimize responsive styles**
6. **Test dark theme**

### Ví dụ migration:
```css
/* CŨ - Dashboard.css */
.summary-card {
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* MỚI - pages/_dashboard.css */
.dashboard-summary-card {
  background-color: var(--content-bg);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
}
```

---

**Tác giả**: Augment Agent  
**Ngày tạo**: 2024  
**Version**: 1.0.0
