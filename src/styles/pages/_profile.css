/* ===================================
   PROFILE PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.profile-container {
  padding: var(--spacing-2xl);
  max-width: 1200px;
  margin: 0 auto;
}

/* ===== PROFILE HEADER ===== */
.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.profile-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
}

/* ===== PROFILE BUTTONS ===== */
.profile-back-button,
.profile-edit-button,
.profile-cancel-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.profile-back-button {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.profile-back-button:hover {
  background-color: var(--hover-bg);
}

.profile-edit-button {
  background-color: var(--primary-blue);
  color: white;
}

.profile-edit-button:disabled {
  background-color: var(--text-secondary);
  opacity: 0.7;
  cursor: not-allowed;
}

.profile-edit-button:hover:not(:disabled) {
  background-color: var(--primary-blue-dark);
}

.profile-cancel-button {
  background-color: var(--error-color);
  color: white;
}

.profile-cancel-button:hover {
  background-color: var(--error-color-dark);
}

/* ===== MESSAGES ===== */
.profile-error-message,
.profile-success-message {
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.profile-error-message {
  background-color: var(--error-bg);
  color: var(--error-text);
  border: 1px solid var(--error-color);
}

.profile-success-message {
  background-color: var(--success-bg);
  color: var(--success-text);
  border: 1px solid var(--success-color);
}

/* ===== PROFILE CONTENT ===== */
.profile-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-2xl);
}

@media (min-width: 768px) {
  .profile-content {
    grid-template-columns: 300px 1fr;
  }
}

/* ===== AVATAR SECTION ===== */
.profile-avatar-section {
  background-color: var(--section-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: var(--card-shadow);
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.profile-avatar-icon {
  width: 120px;
  height: 120px;
  color: var(--secondary-purple);
}

.profile-basic-info h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1.25rem;
  color: var(--text-color);
}

.profile-user-role {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--primary-blue);
  font-weight: 500;
}

.profile-user-username {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.profile-user-job-title {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-color);
  font-size: 0.875rem;
  font-weight: 500;
}

.profile-user-department {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* ===== PROFILE DETAILS ===== */
.profile-details {
  background-color: var(--section-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--card-shadow);
}

.profile-info-section,
.profile-form-section {
  margin-bottom: var(--spacing-2xl);
}

.profile-info-section h3,
.profile-form-section h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 1.125rem;
  color: var(--text-color);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.profile-info-item {
  display: flex;
  margin-bottom: var(--spacing-md);
  align-items: center;
}

.profile-info-label {
  min-width: 150px;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.profile-info-value {
  color: var(--text-color);
}

/* ===== PLATFORMS LIST ===== */
.profile-platforms-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.profile-platform-item {
  background-color: var(--hover-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
}

.profile-platform-name {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.profile-platform-access {
  font-size: 0.875rem;
  color: var(--primary-blue);
}

/* ===== PROFILE FORM ===== */
.profile-edit-form {
  width: 100%;
}

.profile-form-group {
  margin-bottom: var(--spacing-lg);
}

.profile-form-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.profile-form-group input {
  width: 100%;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 1rem;
  transition: border-color var(--transition-fast);
}

.profile-form-group input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.profile-form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-2xl);
}

.profile-save-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  border: none;
  background-color: var(--success-color);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.profile-save-button:hover {
  background-color: var(--success-color-dark);
}

.profile-loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--text-secondary);
  font-size: 1.125rem;
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .profile-avatar-section,
[data-theme="dark"] .profile-details {
  background-color: var(--section-bg);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .profile-platform-item {
  background-color: var(--input-bg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .profile-container {
    padding: var(--spacing-lg);
  }

  .profile-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
  }

  .profile-header h2 {
    text-align: center;
  }

  .profile-back-button,
  .profile-edit-button,
  .profile-cancel-button {
    width: 100%;
    justify-content: center;
  }

  .profile-content {
    gap: var(--spacing-lg);
  }

  .profile-avatar-section,
  .profile-details {
    padding: var(--spacing-lg);
  }

  .profile-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .profile-info-label {
    min-width: auto;
  }

  .profile-platforms-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .profile-container {
    padding: var(--spacing-md);
  }

  .profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50px;
  }

  .profile-avatar-icon {
    width: 100px;
    height: 100px;
  }

  .profile-basic-info h3 {
    font-size: 1.125rem;
  }

  .profile-info-section h3,
  .profile-form-section h3 {
    font-size: 1rem;
  }
}
