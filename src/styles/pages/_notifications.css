/* ===================================
   NOTIFICATIONS PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.notifications-page {
  width: 100%;
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 50vh;
  padding: 0;
}

/* ===== PAGE HEADER ===== */
.notifications-page-header {
  display: grid;
  grid-template-columns: 1fr 3fr 2.5fr 1.5fr;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  gap: var(--spacing-xl);
}

.notifications-page-title {
  display: flex;
  flex-direction: column;
}

.notifications-page-title h2 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.375rem;
  font-weight: 600;
  color: var(--text-color);
}

.notifications-count {
  color: var(--text-secondary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.notifications-unread-badge {
  margin-left: var(--spacing-md);
  padding: 2px var(--spacing-sm);
  background-color: var(--error-color);
  color: white;
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
}

/* ===== FILTERS AND ACTIONS ===== */
.notifications-filters {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  align-items: center;
}

.notifications-filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.notifications-filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
}

.notifications-filter-group select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--input-bg);
  font-size: 0.875rem;
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.notifications-filter-group select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.notifications-actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  justify-content: flex-end;
}

/* ===== SEARCH ===== */
.notifications-search {
  position: relative;
  align-content: center;
  margin-left: auto;
}

.notifications-search-container {
  position: relative;
}

.notifications-search-input {
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 35px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  font-size: 0.875rem;
  width: 250px;
  transition: all var(--transition-normal);
  outline: none;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.notifications-search-input:focus {
  width: 300px;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.notifications-search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1rem;
}

/* ===== REFRESH BUTTON ===== */
.notifications-btn-refresh {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  font-size: 1rem;
  padding: var(--spacing-sm);
  border-radius: var(--radius-xl);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.notifications-btn-refresh:hover {
  background-color: var(--btn-secondary-hover);
}

/* ===== MARK ALL AS READ BUTTON ===== */
.notifications-btn-mark-all-read {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: transparent;
  color: var(--success-color);
  border: 1px solid var(--success-color);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
}

.notifications-btn-mark-all-read:hover {
  background-color: var(--success-bg);
}

/* ===== NOTIFICATIONS CONTAINER ===== */
.notifications-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.notifications-loading,
.notifications-error,
.notifications-empty {
  padding: var(--spacing-3xl);
  text-align: center;
  background-color: var(--section-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
}

.notifications-error {
  color: var(--error-color);
}

/* ===== NOTIFICATION GROUPS ===== */
.notifications-list {
  flex-direction: column;
  gap: var(--spacing-lg);
}

.notifications-group {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--content-bg);
  margin-top: var(--spacing-md);
}

.notifications-group-header {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color var(--transition-fast);
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--section-bg);
  border-bottom: 1px solid var(--border-color);
}

.notifications-group-header:hover {
  background-color: var(--hover-bg);
}

.notifications-group-header h4 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin: 0;
}

.notifications-group-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.notifications-count-badge {
  background-color: var(--hover-bg);
  color: var(--text-secondary);
  padding: 2px var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  min-width: 24px;
  text-align: center;
}

.notifications-collapse-indicator {
  color: var(--text-muted);
  font-size: 1.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

/* ===== NOTIFICATION ITEMS ===== */
.notifications-item {
  display: flex;
  padding: var(--spacing-lg) var(--spacing-xl);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  position: relative;
  border-bottom: 1px solid var(--hover-bg);
}

.notifications-item:last-child {
  border-bottom: none;
}

.notifications-item.highlight {
  background-color: rgba(0, 51, 201, 0.05);
}

.notifications-item:hover {
  background-color: var(--hover-bg);
}

.notifications-item.unread {
  background-color: rgba(0, 51, 201, 0.08);
}

.notifications-item.unread:hover {
  background-color: rgba(0, 51, 201, 0.12);
}

/* ===== NOTIFICATION ICON ===== */
.notifications-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: var(--hover-bg);
  margin-right: var(--spacing-lg);
  flex-shrink: 0;
}

.notifications-icon {
  font-size: 1.125rem;
  color: var(--text-secondary);
}

/* ===== NOTIFICATION CONTENT ===== */
.notifications-content {
  flex: 1;
  min-width: 0;
}

.notifications-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.notifications-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
  background-color: var(--primary-blue);
}

.notifications-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-color);
}

.notifications-time {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-left: var(--spacing-md);
  white-space: nowrap;
}

.notifications-message {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* ===== MARK AS READ BUTTON ===== */
.notifications-btn-mark-read {
  background: none;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--success-color);
  font-size: 1rem;
  margin-left: var(--spacing-md);
  transition: background-color var(--transition-fast);
  flex-shrink: 0;
}

.notifications-btn-mark-read:hover {
  background-color: var(--success-bg);
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .notifications-group {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .notifications-group-header {
  background-color: var(--content-bg);
}

[data-theme="dark"] .notifications-group-header:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .notifications-group-header h4,
[data-theme="dark"] .notifications-message {
  color: var(--text-secondary);
}

[data-theme="dark"] .notifications-item {
  border-color: var(--border-color);
}

[data-theme="dark"] .notifications-item:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .notifications-item.unread {
  background-color: rgba(0, 51, 201, 0.15);
}

[data-theme="dark"] .notifications-item.unread:hover {
  background-color: rgba(0, 51, 201, 0.2);
}

[data-theme="dark"] .notifications-item.highlight {
  background-color: rgba(0, 51, 201, 0.1);
}

[data-theme="dark"] .notifications-icon-container {
  background-color: var(--input-bg);
}

[data-theme="dark"] .notifications-icon {
  color: var(--text-secondary);
}

[data-theme="dark"] .notifications-title {
  color: var(--text-color);
}

[data-theme="dark"] .notifications-time {
  color: var(--text-muted);
}

[data-theme="dark"] .notifications-count-badge {
  background-color: var(--input-bg);
  color: var(--text-secondary);
}

[data-theme="dark"] .notifications-filter-group select {
  background-color: var(--input-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .notifications-page-header {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
  }

  .notifications-search {
    grid-column: 1 / -1;
    grid-row: 1;
    margin-bottom: var(--spacing-lg);
    margin-left: 0;
  }

  .notifications-filters {
    grid-column: 1 / -1;
    grid-row: 2;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .notifications-page-title {
    grid-column: 1;
    grid-row: 3;
  }

  .notifications-actions {
    grid-column: 2;
    grid-row: 3;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .notifications-page-header {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    gap: var(--spacing-lg);
  }

  .notifications-search,
  .notifications-filters,
  .notifications-page-title,
  .notifications-actions {
    grid-column: 1;
  }

  .notifications-search {
    grid-row: 1;
  }

  .notifications-filters {
    grid-row: 2;
    justify-content: flex-start;
  }

  .notifications-page-title {
    grid-row: 3;
  }

  .notifications-actions {
    grid-row: 4;
    justify-content: flex-start;
  }

  .notifications-search-input,
  .notifications-search-input:focus {
    width: 100%;
  }

  .notifications-item {
    padding: var(--spacing-md);
  }

  .notifications-group-header {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .notifications-page-header {
    padding: var(--spacing-md);
  }

  .notifications-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .notifications-filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .notifications-filter-group select {
    width: 60%;
    min-width: auto;
  }

  .notifications-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .notifications-btn-mark-all-read,
  .notifications-btn-refresh {
    width: 100%;
    justify-content: center;
  }

  .notifications-item {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .notifications-icon-container {
    align-self: flex-start;
    margin-right: 0;
  }

  .notifications-header {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .notifications-time {
    margin-left: 0;
    order: 3;
    width: 100%;
  }
}
