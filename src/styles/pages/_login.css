/* ===================================
   LOGIN PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-color);
  transition: background-color var(--transition-normal), color var(--transition-normal);
  position: relative;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  background-image: radial-gradient(circle at 10% 20%, rgba(0, 51, 201, 0.05) 0%, rgba(111, 12, 223, 0.05) 90%);
}

/* ===== THEME TOGGLE ===== */
.login-theme-toggle {
  position: absolute;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  z-index: var(--z-fixed);
}

/* ===== LOGIN CARD ===== */
.login-card-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 450px;
}

.login-card {
  background-color: var(--content-bg);
  border-radius: var(--spacing-xl);
  box-shadow: var(--card-shadow);
  width: 100%;
  padding: 40px;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow-hover);
}

/* ===== FEATURE ICONS ===== */
.login-feature-icons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin: var(--spacing-2xl) 0;
}

.login-feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: var(--radius-lg);
  font-size: 1.5rem;
  color: white;
  transition: all var(--transition-normal);
}

.login-feature-icon:hover {
  transform: translateY(-5px) rotate(5deg);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.login-feature-icon.bg-primary-green {
  background-color: var(--primary-green);
}

.login-feature-icon.bg-secondary-light-blue {
  background-color: var(--secondary-light-blue);
}

.login-feature-icon.bg-secondary-yellow {
  background-color: var(--secondary-yellow);
}

.login-feature-icon.bg-secondary-purple {
  background-color: var(--secondary-purple);
}

/* ===== LOGIN HEADER ===== */
.login-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.login-header h1 {
  color: var(--primary-blue);
  margin-bottom: var(--spacing-md);
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* ===== ERROR MESSAGE ===== */
.login-error {
  background-color: var(--error-bg);
  color: var(--error-text);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-2xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 0.875rem;
}

/* ===== LOGIN FORM ===== */
.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.login-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.login-form-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9375rem;
}

.login-form-group input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 0.9375rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.login-form-group input:focus {
  border-color: var(--primary-blue);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
  transform: translateY(-2px);
}

/* ===== PASSWORD INPUT ===== */
.login-password-input-container {
  position: relative;
  width: 100%;
}

.login-password-input-container input {
  width: 100%;
  padding-right: 40px;
}

.login-password-toggle {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color var(--transition-fast);
}

.login-password-toggle:hover {
  color: var(--text-color);
}

.login-password-toggle:focus {
  outline: none;
}

/* ===== FORM FOOTER ===== */
.login-form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  margin-top: var(--spacing-sm);
}

.login-remember-me {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.login-remember-me input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary-blue);
}

.login-remember-me label {
  cursor: pointer;
  color: var(--text-secondary);
}

.login-forgot-password {
  color: var(--primary-blue);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.login-forgot-password:hover {
  text-decoration: underline;
  transform: translateY(-2px);
}

/* ===== LOGIN BUTTON ===== */
.login-button {
  background-color: var(--primary-blue);
  color: white;
  border: none;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  margin-top: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: all 0.6s ease;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  background-color: var(--btn-primary-hover);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 51, 201, 0.3);
}

.login-button:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(0, 51, 201, 0.2);
}

.login-button:disabled {
  background-color: rgba(0, 51, 201, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .login-container {
  background-image: radial-gradient(circle at 10% 20%, rgba(0, 51, 201, 0.1) 0%, rgba(111, 12, 223, 0.1) 90%);
}

[data-theme="dark"] .login-card {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .login-card:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .login-header h1 {
  background: linear-gradient(135deg, var(--secondary-light-blue), var(--secondary-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[data-theme="dark"] .login-form-group input:focus {
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.2);
}

[data-theme="dark"] .login-forgot-password {
  color: var(--secondary-light-blue);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .login-container {
    padding: var(--spacing-lg);
  }

  .login-theme-toggle {
    top: var(--spacing-lg);
    right: var(--spacing-lg);
  }

  .login-card {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }

  .login-header h1 {
    font-size: 1.75rem;
  }

  .login-feature-icons {
    gap: var(--spacing-md);
  }

  .login-feature-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: var(--spacing-md);
  }

  .login-card {
    padding: var(--spacing-xl) var(--spacing-md);
  }

  .login-form-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .login-header h1 {
    font-size: 1.5rem;
  }

  .login-feature-icons {
    gap: var(--spacing-sm);
  }

  .login-feature-icon {
    width: 36px;
    height: 36px;
    font-size: 1.125rem;
  }

  .login-form-group input {
    font-size: 1rem; /* Prevent zoom on iOS */
  }
}
