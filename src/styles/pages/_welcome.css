/* ===================================
   WELCOME PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.welcome-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3xl);
  position: relative;
  overflow: hidden;
}

.welcome-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

/* ===== WELCOME CONTENT ===== */
.welcome-content {
  background: var(--content-bg);
  border-radius: var(--spacing-xl);
  padding: var(--spacing-3xl);
  max-width: 800px;
  width: 100%;
  box-shadow: var(--modal-shadow);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== WELCOME HEADER ===== */
.welcome-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.welcome-logo h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--secondary-light-blue), var(--secondary-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-lg);
}

.welcome-text h2 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.welcome-text h3 {
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.welcome-text p {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* ===== PROGRESS SECTION ===== */
.welcome-progress {
  background: var(--section-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3xl);
  border: 1px solid var(--border-color);
}

.welcome-progress-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.welcome-progress-header h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

/* ===== PROGRESS STEPS ===== */
.welcome-progress-steps {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.welcome-progress-step {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  border: 2px solid transparent;
}

.welcome-progress-step.pending {
  background: var(--hover-bg);
  opacity: 0.6;
}

.welcome-progress-step.active {
  background: linear-gradient(135deg, rgba(0, 207, 106, 0.1), rgba(0, 51, 201, 0.1));
  border-color: var(--success-color);
  box-shadow: 0 4px 20px rgba(0, 207, 106, 0.2);
}

.welcome-progress-step.completed {
  background: linear-gradient(135deg, rgba(0, 207, 106, 0.15), rgba(0, 51, 201, 0.15));
  border-color: var(--success-color);
}

/* ===== STEP ICONS ===== */
.welcome-step-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
  transition: all var(--transition-normal);
}

.welcome-progress-step.pending .welcome-step-icon {
  background: var(--content-bg);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.welcome-progress-step.active .welcome-step-icon {
  background: var(--success-color);
  color: white;
  animation: welcome-pulse 2s infinite;
}

.welcome-progress-step.completed .welcome-step-icon {
  background: var(--success-color);
  color: white;
}

/* ===== STEP CONTENT ===== */
.welcome-step-content {
  flex: 1;
}

.welcome-step-content h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 var(--spacing-sm) 0;
}

.welcome-step-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* ===== COMPLETION SECTION ===== */
.welcome-progress-complete {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-top: var(--spacing-3xl);
  padding-top: var(--spacing-3xl);
  border-top: 1px solid var(--border-color);
}

.welcome-complete-message {
  margin-bottom: var(--spacing-3xl);
}

.welcome-complete-icon {
  font-size: 2rem;
  color: var(--success-color);
  margin-bottom: var(--spacing-lg);
  animation: welcome-bounceIn 0.6s ease;
}

.welcome-complete-message h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 var(--spacing-sm) 0;
}

.welcome-complete-message p {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

/* ===== GET STARTED BUTTON ===== */
.welcome-btn-get-started {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-3xl);
  background: linear-gradient(135deg, var(--success-color), var(--primary-blue));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  min-width: 150px;
}

.welcome-btn-get-started:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 207, 106, 0.3);
}

.welcome-btn-get-started:active {
  transform: translateY(0);
}

/* ===== ANIMATIONS ===== */
@keyframes welcome-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes welcome-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 207, 106, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 207, 106, 0);
  }
}

@keyframes welcome-bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.welcome-spin {
  animation: welcome-spin 1s linear infinite;
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .welcome-container {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

[data-theme="dark"] .welcome-content {
  background: var(--bg-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .welcome-progress {
  background: var(--section-bg);
  border-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .welcome-container {
    padding: var(--spacing-lg);
  }

  .welcome-content {
    padding: var(--spacing-3xl);
  }

  .welcome-logo h1 {
    font-size: 2rem;
  }

  .welcome-text h2 {
    font-size: 1.5rem;
  }

  .welcome-text h3 {
    font-size: 1.125rem;
  }

  .welcome-progress-step {
    padding: var(--spacing-lg);
  }

  .welcome-step-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .welcome-container {
    padding: var(--spacing-md);
  }

  .welcome-content {
    padding: var(--spacing-xl);
  }

  .welcome-progress {
    padding: var(--spacing-xl);
  }

  .welcome-logo h1 {
    font-size: 1.75rem;
  }

  .welcome-text h2 {
    font-size: 1.25rem;
  }

  .welcome-progress-step {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .welcome-btn-get-started {
    width: 100%;
  }
}
