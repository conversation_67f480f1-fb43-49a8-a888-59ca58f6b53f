/* ===================================
   USERS PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.users-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* ===== USERS HEADER ===== */
.users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-xl);
  background-color: var(--section-bg);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
}

.users-filters {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
  align-items: center;
}

.users-filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.users-filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
}

.users-filter-group select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  min-width: 120px;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.users-filter-group select:hover {
  border-color: var(--primary-blue);
}

.users-filter-group select:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
  outline: none;
}

.users-actions {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.users-actions-buttons {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

/* ===== COLUMN SELECTOR ===== */
.users-column-selector-container {
  position: relative;
}

.users-column-selector-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background-color: var(--input-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.users-column-selector-btn:hover {
  background-color: var(--btn-secondary-hover);
  border-color: var(--primary-blue);
}

.users-column-selector-btn:focus,
.users-column-selector-btn.active {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
  outline: none;
}

.users-column-selector-dropdown {
  position: absolute;
  top: calc(100% + var(--spacing-sm));
  left: 0;
  width: 250px;
  background-color: var(--content-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--card-shadow);
  z-index: var(--z-dropdown);
  overflow: hidden;
  animation: fadeIn 0.2s ease;
}

.users-column-selector-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.users-column-selector-header h4 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-color);
}

.users-column-selector-list {
  max-height: 300px;
  overflow-y: auto;
}

.users-column-selector-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.users-column-selector-item:hover {
  background-color: var(--hover-bg);
}

.users-column-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--input-bg);
  transition: all var(--transition-fast);
}

.users-column-selector-item:hover .users-column-checkbox {
  border-color: var(--primary-blue);
}

.users-column-check-icon {
  color: var(--primary-blue);
  font-size: 0.875rem;
}

/* ===== USERS TABLE ===== */
.users-table-container {
  overflow-x: auto;
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-normal);
}

.users-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.users-table td {
  padding: var(--spacing-xs);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.users-table th {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
  font-weight: 600;
  color: var(--text-secondary);
  cursor: pointer;
  user-select: none;
  position: relative;
  border-top: none;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.users-table th.sorted {
  color: var(--secondary-purple);
  background-color: rgba(0, 51, 201, 0.05);
}

.users-sort-icon {
  margin-left: var(--spacing-sm);
  font-size: 0.875rem;
  vertical-align: middle;
  transition: all var(--transition-fast);
}

.users-sort-inactive {
  opacity: 0.3;
}

.users-table th:hover {
  background-color: var(--hover-bg);
}

.users-table tr:last-child td {
  border-bottom: none;
}

.users-table tbody tr {
  transition: all var(--transition-fast);
}

.users-table tbody tr:hover {
  background-color: var(--hover-bg);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  z-index: 1;
  position: relative;
}

/* ===== ROLE BADGES ===== */
.users-role-badge {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}

.users-role-badge.admin {
  background-color: #e3f2fd;
  color: #2196f3;
}

.users-role-badge.approver {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.users-role-badge.user {
  background-color: var(--hover-bg);
  color: var(--text-secondary);
}

/* ===== USER FORM ===== */
.users-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.users-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.users-form-group label {
  font-weight: 500;
  color: var(--text-secondary);
}

.users-form-group input,
.users-form-group select {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.users-role-description {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-style: italic;
  margin-top: calc(-1 * var(--spacing-md));
  margin-bottom: var(--spacing-md);
}

/* ===== ARRAY VALUES ===== */
.users-array-values-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.users-array-value-item {
  border-radius: var(--radius-sm);
  padding: 2px var(--spacing-sm);
  font-size: 0.85em;
  white-space: nowrap;
}

/* Default array value style */
.users-array-value-default {
  background-color: var(--hover-bg);
  color: var(--text-secondary);
}

/* Role-based colors */
.users-array-value-admin {
  background-color: #e4abdd;
  color: #840e51;
}

.users-array-value-approver {
  background-color: #edd9c4;
  color: #ca6f0e;
}

.users-array-value-user {
  background-color: #a8c1e7;
  color: #2475ee;
}

/* Platform-based colors */
.users-array-value-whitelist {
  background-color: #e8eaf6;
  color: #1876f1;
}

.users-array-value-all {
  background-color: #e8eaf6;
  color: #f11818;
}

.users-array-value-funnel {
  background-color: #fff3e0;
  color: #d24fc0;
}

.users-array-value-zns {
  background-color: #fff3e0;
  color: #564738;
}

.users-array-value-sbv {
  background-color: #fce4ec;
  color: #c2185b;
}

.users-array-value-ads {
  background-color: #e0f7fa;
  color: #0097a7;
}

.users-array-value-config {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.users-array-value-tnex {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.users-array-value-shinhan {
  background-color: var(--success-bg);
  color: #dca358;
}

.users-array-value-samsung {
  background-color: var(--success-bg);
  color: #1dd5f1;
}

/* Status-based colors */
.users-array-value-active {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.users-array-value-inactive {
  background-color: rgba(158, 158, 158, 0.15);
  color: #9e9e9e;
}

.users-array-value-removed {
  background-color: var(--error-bg);
  color: var(--error-color);
}

/* ===== STATUS BADGES ===== */
.users-status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.users-status-badge.active {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.users-status-badge.inactive {
  background-color: rgba(158, 158, 158, 0.15);
  color: #9e9e9e;
}

.users-status-badge.pending {
  background-color: var(--warning-bg);
  color: var(--warning-color);
}

.users-status-badge.removed {
  background-color: var(--error-bg);
  color: var(--error-color);
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .users-header,
[data-theme="dark"] .users-table-container {
  background-color: var(--section-bg);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .users-column-selector-dropdown {
  background-color: var(--content-bg);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .users-column-selector-header {
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .users-table th {
  background-color: var(--content-bg);
  color: var(--text-secondary);
}

[data-theme="dark"] .users-table th:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .users-table tbody tr:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .users-role-badge.user {
  background-color: var(--input-bg);
  color: var(--text-secondary);
}

[data-theme="dark"] .users-array-value-default {
  background-color: var(--input-bg);
  color: var(--text-secondary);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .users-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .users-filters {
    width: 100%;
    margin-bottom: var(--spacing-lg);
    overflow-x: auto;
    padding-bottom: var(--spacing-md);
    flex-wrap: nowrap;
  }

  .users-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .users-filter-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .users-filter-group select {
    width: 100%;
  }

  .users-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-lg);
  }

  .users-actions-buttons {
    width: 100%;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .users-column-selector-container {
    width: 100%;
  }

  .users-column-selector-btn {
    width: 100%;
    justify-content: center;
  }

  .users-column-selector-dropdown {
    width: 100%;
    position: relative;
    margin-top: var(--spacing-sm);
  }

  .users-search-box {
    width: 100%;
  }

  .users-search-box input,
  .users-search-box input:focus {
    width: 100%;
  }

  .btn-primary {
    width: 100%;
    justify-content: center;
  }

  .pagination-container {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .pagination {
    width: 100%;
    justify-content: center;
  }

  .users-table-container {
    border-radius: var(--radius-md);
  }

  .users-table th,
  .users-table td {
    padding: var(--spacing-sm);
    font-size: 0.8125rem;
  }
}

@media (max-width: 480px) {
  .users-container {
    gap: var(--spacing-lg);
  }

  .users-header {
    padding: var(--spacing-lg);
  }

  .users-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .users-filter-group {
    width: 100%;
  }

  .users-filter-group select {
    min-width: auto;
  }

  .users-array-values-container {
    gap: 2px;
  }

  .users-array-value-item {
    font-size: 0.75rem;
    padding: 1px var(--spacing-xs);
  }

  .users-status-badge {
    font-size: 0.6875rem;
    padding: 2px var(--spacing-xs);
  }

  .users-role-badge {
    font-size: 0.6875rem;
    padding: 2px var(--spacing-xs);
  }

  /* Hide less important columns on mobile */
  .users-table th:nth-child(n+4),
  .users-table td:nth-child(n+4) {
    display: none;
  }

  /* Show only essential columns */
  .users-table th:nth-child(1),
  .users-table th:nth-child(2),
  .users-table th:nth-child(3),
  .users-table td:nth-child(1),
  .users-table td:nth-child(2),
  .users-table td:nth-child(3) {
    display: table-cell;
  }
}
