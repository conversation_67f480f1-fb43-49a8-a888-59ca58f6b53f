/* ===================================
   EVENT MANAGEMENT PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.event-management-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  background-color: var(--bg-color);
  min-height: 100vh;
}

/* ===== HEADER ===== */
.event-management-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-xl);
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
}

.event-management-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

/* ===== MESSAGES ===== */
.event-management-success-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--success-bg);
  color: var(--success-color);
  border: 1px solid var(--success-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xl);
  font-size: 0.875rem;
  font-weight: 500;
}

.event-management-error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--error-bg);
  color: var(--error-color);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xl);
  font-size: 0.875rem;
  font-weight: 500;
}

/* ===== FILTER AND SEARCH BAR ===== */
.event-management-filter-search-bar {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  flex-wrap: wrap;
}

.event-management-search-container {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.event-management-search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) 45px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.event-management-search-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
}

.event-management-search-icon {
  position: absolute;
  left: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 1rem;
}

.event-management-search-icon.spinning {
  animation: event-management-spin 1s linear infinite;
}

.event-management-clear-search-icon {
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1rem;
  transition: color var(--transition-fast);
  padding: var(--spacing-xs);
  border-radius: var(--radius-full);
}

.event-management-clear-search-icon:hover {
  color: var(--text-color);
  background-color: var(--hover-bg);
}

/* ===== FILTER CONTROLS ===== */
.event-management-filter-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.event-management-filter-container label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
}

.event-management-filter-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 120px;
}

.event-management-filter-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
}

.event-management-actions-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.event-management-clear-filters-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--warning-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.event-management-clear-filters-btn:hover {
  background-color: var(--warning-color);
  opacity: 0.9;
  transform: translateY(-1px);
}

/* ===== RESULTS SUMMARY ===== */
.event-management-results-summary {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--info-bg);
  color: var(--info-color);
  border: 1px solid var(--info-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
}

/* ===== LOADING STATES ===== */
.event-management-loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xl);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.event-management-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px var(--spacing-xl);
  text-align: center;
  color: var(--text-secondary);
}

.event-management-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.event-management-empty-state p {
  font-size: 1rem;
  margin-bottom: var(--spacing-xl);
}

/* ===== EVENT TABLE ===== */
.event-management-table-container {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}

.event-management-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.event-management-table th {
  background-color: var(--section-bg);
  color: var(--text-color);
  font-weight: 600;
  padding: var(--spacing-lg) var(--spacing-md);
  text-align: left;
  border-bottom: 2px solid var(--border-color);
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  transition: background-color var(--transition-fast);
}

.event-management-table th:hover {
  background-color: var(--hover-bg);
}

.event-management-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
  vertical-align: middle;
}

.event-management-table tr:hover {
  background-color: var(--hover-bg);
}

.event-management-table tr:last-child td {
  border-bottom: none;
}

/* ===== SORTABLE HEADERS ===== */
.event-management-sortable-header {
  cursor: pointer;
  user-select: none;
  transition: background-color var(--transition-fast);
}

.event-management-sortable-header:hover {
  background-color: var(--hover-bg);
}

.event-management-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-sm);
}

.event-management-sort-indicator {
  display: flex;
  align-items: center;
  color: var(--primary-blue);
  font-size: 0.875rem;
}

/* ===== STATUS BADGES ===== */
.event-management-status-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.event-management-status-badge.active {
  background-color: var(--success-bg);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.event-management-status-badge.inactive {
  background-color: var(--error-bg);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

/* ===== ACTION BUTTONS ===== */
.event-management-btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--primary-blue);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.event-management-btn-icon:hover {
  background-color: var(--primary-blue-dark);
  transform: translateY(-1px);
}

/* ===== CLICKABLE ROWS ===== */
.event-management-clickable-row {
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.event-management-clickable-row:hover {
  background-color: var(--hover-bg) !important;
}

/* ===== SCROLL SENTINEL ===== */
.event-management-scroll-sentinel {
  height: 1px;
  margin-top: var(--spacing-xl);
}

/* ===== COLUMN SELECTOR ===== */
.event-management-column-selector-dropdown {
  position: relative;
  background-color: var(--content-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  margin-bottom: var(--spacing-xl);
  z-index: var(--z-dropdown);
}

.event-management-column-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
}

.event-management-column-selector-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.event-management-close-selector-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: transparent;
  color: var(--text-secondary);
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.event-management-close-selector-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

.event-management-column-selector-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
}

.event-management-column-checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.event-management-column-checkbox:hover {
  background-color: var(--hover-bg);
}

.event-management-column-checkbox input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
  accent-color: var(--primary-blue);
}

.event-management-column-checkbox span {
  font-size: 0.875rem;
  color: var(--text-color);
  user-select: none;
}

/* ===== MODAL STYLES ===== */
.event-management-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--spacing-xl);
}

.event-management-modal-content {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--modal-shadow);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.event-management-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-2xl);
  border-bottom: 1px solid var(--border-color);
}

.event-management-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.event-management-modal-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: transparent;
  color: var(--text-secondary);
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.event-management-modal-close-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

.event-management-modal-body {
  padding: var(--spacing-2xl);
  overflow-y: auto;
  flex: 1;
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .event-management-header,
[data-theme="dark"] .event-management-filter-search-bar,
[data-theme="dark"] .event-management-table-container,
[data-theme="dark"] .event-management-column-selector-dropdown,
[data-theme="dark"] .event-management-modal-content {
  background-color: var(--section-bg);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .event-management-table th {
  background-color: var(--content-bg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .event-management-container {
    padding: var(--spacing-lg);
  }

  .event-management-filter-search-bar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
  }

  .event-management-search-container {
    min-width: auto;
  }

  .event-management-filter-container {
    justify-content: space-between;
  }

  .event-management-actions-container {
    justify-content: center;
  }

  .event-management-table-container {
    overflow-x: auto;
  }

  .event-management-column-selector-content {
    grid-template-columns: 1fr;
  }

  .event-management-modal-content {
    margin: var(--spacing-md);
    max-width: calc(100% - 32px);
  }
}

@media (max-width: 480px) {
  .event-management-container {
    padding: var(--spacing-md);
  }

  .event-management-header {
    padding: var(--spacing-lg);
  }

  .event-management-filter-search-bar {
    padding: var(--spacing-lg);
  }

  .event-management-title {
    font-size: 1.5rem;
  }

  .event-management-table th,
  .event-management-table td {
    padding: var(--spacing-sm);
    font-size: 0.8125rem;
  }

  .event-management-modal-header,
  .event-management-modal-body {
    padding: var(--spacing-lg);
  }
}

/* ===== ANIMATIONS ===== */
@keyframes event-management-spin {
  from { transform: translateY(-50%) rotate(0deg); }
  to { transform: translateY(-50%) rotate(360deg); }
}
