/* ===================================
   RELEASE NOTES PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.release-notes-container {
  margin: 0 auto;
  position: relative;
}

/* ===== MESSAGES ===== */
.release-notes-success-message {
  position: fixed;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  background-color: var(--success-color);
  color: white;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-sm);
  box-shadow: var(--modal-shadow);
  z-index: var(--z-modal);
  animation: fadeIn 0.3s ease-in-out, fadeOut 0.3s ease-in-out 2.7s;
}

.release-notes-error-message {
  background-color: var(--error-color);
  color: white;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--card-shadow);
  animation: fadeIn 0.3s ease-in-out;
}

/* ===== HEADER ===== */
.release-notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.release-notes-actions {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

/* ===== STATUS FILTER ===== */
.release-notes-status-filter {
  display: flex;
  border-radius: var(--radius-sm);
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.release-notes-status-filter button {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.release-notes-status-filter button.active {
  background-color: var(--primary-blue);
  color: white;
}

.release-notes-status-filter button:hover:not(.active) {
  background-color: var(--btn-secondary-hover);
}

/* ===== ACTION BUTTONS ===== */
.release-notes-action-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-left: auto;
}

.release-notes-add-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--success-color);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.release-notes-add-btn:hover {
  background-color: var(--success-color-dark);
  transform: translateY(-1px);
}

.release-notes-refresh-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--primary-blue);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.release-notes-refresh-btn:hover {
  background-color: var(--primary-blue-dark);
  transform: translateY(-1px);
}

/* ===== MAIN LAYOUT ===== */
.release-notes-list {
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: var(--spacing-2xl);
}

.release-notes-items-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

/* ===== VERSION TREE ===== */
.release-notes-version-tree-container {
  background-color: var(--content-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-2xl);
  position: sticky;
  top: var(--spacing-2xl);
  max-height: calc(100vh - 48px);
  overflow-y: auto;
  border: 1px solid var(--primary-blue);
}

.release-notes-version-tree-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.release-notes-version-tree {
  margin-top: var(--spacing-lg);
}

.release-notes-version-tree-node {
  margin-bottom: var(--spacing-sm);
}

.release-notes-version-tree-parent {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-sm) 0;
  font-weight: 500;
  color: var(--text-color);
  transition: color var(--transition-fast);
}

.release-notes-version-tree-parent:hover {
  color: var(--primary-blue);
}

.release-notes-version-tree-parent.active {
  color: var(--primary-blue);
  font-weight: 600;
}

.release-notes-version-tree-toggle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  transition: transform var(--transition-fast);
}

.release-notes-version-tree-toggle.expanded {
  transform: rotate(90deg);
}

.release-notes-version-tree-children {
  margin-left: var(--spacing-2xl);
  height: 0;
  overflow: hidden;
  transition: height var(--transition-normal);
}

.release-notes-version-tree-children.expanded {
  height: auto;
}

/* ===== RELEASE ITEMS ===== */
.release-notes-item {
  background-color: var(--content-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-2xl);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--primary-blue);
  transition: all var(--transition-fast);
}

.release-notes-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.release-notes-item.released {
  border-color: var(--success-color);
}

.release-notes-item.upcoming {
  border-color: var(--primary-blue);
}

/* ===== ITEM HEADER ===== */
.release-notes-item-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.release-notes-item-version {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  min-width: 200px;
}

.release-notes-item-version h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
}

.release-notes-item-version input {
  width: 100%;
  padding: var(--spacing-md);
  font-size: 1.125rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--input-bg);
  color: var(--text-color);
}

.release-notes-item-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

/* ===== STATUS BADGES ===== */
.release-notes-status {
  font-size: 0.875rem;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  font-weight: 500;
  display: inline-block;
}

.release-notes-status.released {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.release-notes-status.upcoming {
  background-color: var(--info-bg);
  color: var(--info-color);
}

.release-notes-date {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* ===== PROGRESS BAR ===== */
.release-notes-progress {
  width: 100%;
  margin-bottom: var(--spacing-md);
  margin-top: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.release-notes-progress.edit-mode {
  position: relative;
  padding: var(--spacing-md) 0;
}

.release-notes-progress-container {
  position: relative;
  height: 20px;
  display: flex;
  align-items: center;
}

.release-notes-progress-bar {
  width: 100%;
  height: 5px;
  background-color: var(--hover-bg);
  border-radius: var(--radius-sm);
  overflow: visible;
  position: relative;
}

.release-notes-progress-fill {
  height: 100%;
  background-color: var(--primary-blue);
  border-radius: var(--radius-sm);
  transition: width var(--transition-normal);
}

.release-notes-item.released .release-notes-progress-fill {
  background-color: var(--success-color);
}

.release-notes-progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background-color: var(--primary-blue);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.release-notes-item.released .release-notes-progress-thumb {
  background-color: var(--success-color);
}

.release-notes-progress-slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 20px;
  opacity: 0;
  cursor: pointer;
  z-index: 3;
}

.release-notes-progress-text {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-align: right;
  margin-top: var(--spacing-sm);
}

/* ===== ITEM CONTENT ===== */
.release-notes-item-content {
  margin-bottom: var(--spacing-2xl);
}

.release-notes-item-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* ===== STATUS SELECTION ===== */
.release-notes-item-status {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.release-notes-item-status label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: 0.875rem;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  background-color: var(--hover-bg);
  transition: all var(--transition-fast);
}

.release-notes-item-status input[type="radio"] {
  margin: 0;
  accent-color: var(--primary-blue);
}

.release-notes-item-status label:has(input:checked) {
  background-color: var(--info-bg);
  color: var(--info-color);
}

.release-notes-item.released .release-notes-item-status label:has(input[value="released"]:checked) {
  background-color: var(--success-bg);
  color: var(--success-color);
}

/* ===== DATE INPUT ===== */
.release-notes-item-date {
  display: flex;
  align-items: center;
}

.release-notes-item-date input {
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 0.875rem;
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .release-notes-version-tree-container {
  background-color: var(--section-bg);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .release-notes-item {
  background-color: var(--section-bg);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .release-notes-status-filter button {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
}

[data-theme="dark"] .release-notes-status-filter button.active {
  background-color: var(--primary-blue);
  color: white;
}

[data-theme="dark"] .release-notes-item-status label {
  background-color: var(--input-bg);
}

[data-theme="dark"] .release-notes-progress-bar {
  background-color: var(--input-bg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .release-notes-list {
    grid-template-columns: 1fr;
  }

  .release-notes-version-tree-container {
    position: relative;
    top: 0;
    max-height: none;
    margin-bottom: var(--spacing-2xl);
  }

  .release-notes-header {
    flex-direction: column;
    align-items: stretch;
  }

  .release-notes-actions {
    justify-content: space-between;
  }

  .release-notes-action-buttons {
    margin-left: 0;
    justify-content: flex-end;
  }

  .release-notes-item {
    padding: var(--spacing-lg);
  }

  .release-notes-item-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .release-notes-item-version {
    min-width: auto;
  }

  .release-notes-item-meta {
    justify-content: space-between;
  }

  .release-notes-item-status {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .release-notes-item-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .release-notes-container {
    padding: var(--spacing-md);
  }

  .release-notes-version-tree-container {
    padding: var(--spacing-lg);
  }

  .release-notes-item {
    padding: var(--spacing-md);
  }

  .release-notes-header {
    gap: var(--spacing-md);
  }

  .release-notes-actions {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .release-notes-status-filter {
    width: 100%;
  }

  .release-notes-status-filter button {
    flex: 1;
    text-align: center;
  }

  .release-notes-action-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .release-notes-add-btn,
  .release-notes-refresh-btn {
    flex: 1;
    justify-content: center;
  }

  .release-notes-item-version h2 {
    font-size: 1.25rem;
  }

  .release-notes-item-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}
