/* ===================================
   TICKETS PAGE STYLES
   ================================= */

/* ===== TICKETS FILTERS ===== */
.tickets-filters {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
}

.tickets-filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.tickets-filter-group label {
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
}

.tickets-filter-group select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 0.875rem;
  min-width: 120px;
  transition: all var(--transition-fast);
}

.tickets-filter-group select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

/* ===== TICKETS TABLE ===== */
.tickets-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-top: var(--spacing-lg);
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.tickets-table th,
.tickets-table td {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.tickets-table th {
  background-color: var(--section-bg);
  font-weight: 600;
  color: var(--text-secondary);
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background-color var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tickets-table th:hover {
  background-color: var(--hover-bg);
}

.tickets-table tr:hover {
  background-color: var(--hover-bg);
}

.tickets-table tr:last-child td {
  border-bottom: none;
}

/* ===== SORT ICONS ===== */
.tickets-sort-icon {
  margin-left: var(--spacing-xs);
  font-size: 0.875rem;
  vertical-align: middle;
  transition: opacity var(--transition-fast);
}

.tickets-sort-inactive {
  opacity: 0.3;
}

/* ===== ACTION BUTTONS ===== */
.tickets-action-buttons {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
}

.tickets-btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  color: var(--text-secondary);
}

.tickets-btn-icon.view:hover {
  background-color: var(--info-bg);
  color: var(--info-color);
}

.tickets-btn-icon.edit:hover {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.tickets-btn-icon.delete:hover {
  background-color: var(--error-bg);
  color: var(--error-color);
}

.tickets-btn-icon.print:hover {
  background-color: var(--warning-bg);
  color: var(--warning-color);
}

/* ===== PAGINATION ===== */
.tickets-pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-xl);
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.tickets-pagination-info {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.tickets-pagination {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xs);
}

.tickets-pagination-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  background-color: var(--content-bg);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: var(--text-color);
}

.tickets-pagination-btn:hover:not(:disabled) {
  background-color: var(--hover-bg);
  border-color: var(--primary-blue);
}

.tickets-pagination-btn.active {
  background-color: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.tickets-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ===== TICKET DETAILS ===== */
.tickets-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.tickets-detail-row {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.tickets-detail-label {
  width: 120px;
  font-weight: 500;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.tickets-detail-value {
  flex: 1;
}

.tickets-detail-input,
.tickets-detail-select,
.tickets-detail-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.tickets-detail-input:focus,
.tickets-detail-select:focus,
.tickets-detail-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.tickets-detail-textarea {
  min-height: 100px;
  resize: vertical;
}

/* ===== DELETE CONFIRMATION ===== */
.tickets-delete-confirmation {
  text-align: center;
  padding: var(--spacing-xl) 0;
}

.tickets-delete-confirmation p {
  margin-bottom: var(--spacing-lg);
  color: var(--text-color);
}

.tickets-delete-confirmation .warning-icon {
  font-size: 3rem;
  color: var(--error-color);
  margin-bottom: var(--spacing-lg);
}

/* ===== STATUS BADGES ===== */
.tickets-status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tickets-status-badge.initial {
  background-color: var(--info-bg);
  color: var(--info-color);
}

.tickets-status-badge.approved {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.tickets-status-badge.delivered {
  background-color: var(--warning-bg);
  color: var(--warning-color);
}

.tickets-status-badge.completed {
  background-color: rgba(139, 92, 246, 0.1);
  color: var(--secondary-purple);
}

.tickets-status-badge.cancelled {
  background-color: var(--error-bg);
  color: var(--error-color);
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .tickets-table {
  background-color: var(--section-bg);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .tickets-table th {
  background-color: var(--content-bg);
  color: var(--text-secondary);
}

[data-theme="dark"] .tickets-table th:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .tickets-table tr:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .tickets-filters {
  border-color: var(--border-color);
}

[data-theme="dark"] .tickets-filter-group label {
  color: var(--text-secondary);
}

[data-theme="dark"] .tickets-filter-group select {
  background-color: var(--input-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

[data-theme="dark"] .tickets-pagination-btn {
  background-color: var(--content-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

[data-theme="dark"] .tickets-pagination-btn:hover:not(:disabled) {
  background-color: var(--hover-bg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .tickets-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  
  .tickets-filter-group {
    width: 100%;
    justify-content: space-between;
  }
  
  .tickets-filter-group select {
    width: 60%;
    min-width: auto;
  }

  .tickets-pagination-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .tickets-detail-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .tickets-detail-label {
    width: auto;
  }
}

@media (max-width: 480px) {
  .tickets-table th,
  .tickets-table td {
    padding: var(--spacing-sm);
    font-size: 0.8125rem;
  }
  
  .tickets-action-buttons {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .tickets-btn-icon {
    padding: var(--spacing-xs);
    font-size: 0.875rem;
  }

  .tickets-pagination {
    flex-wrap: wrap;
  }

  .tickets-pagination-btn {
    min-width: 36px;
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}
