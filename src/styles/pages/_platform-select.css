/* ===================================
   PLATFORM SELECT PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.platform-select-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  background-color: var(--bg-color);
}

/* ===== HEADER ===== */
.platform-select-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-2xl);
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
}

.platform-select-header h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.platform-select-header p {
  font-size: 1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* ===== PLATFORM GRID ===== */
.platform-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

/* ===== PLATFORM CARDS ===== */
.platform-card {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all var(--transition-normal);
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  position: relative;
}

.platform-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-blue), var(--secondary-purple));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.platform-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--card-shadow-hover);
}

.platform-card:hover::before {
  opacity: 1;
}

.platform-card:active {
  transform: translateY(-4px) scale(0.98);
}

/* ===== PLATFORM IMAGE ===== */
.platform-image-container {
  height: 200px;
  overflow: hidden;
  background: linear-gradient(135deg, var(--hover-bg), var(--section-bg));
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md);
  position: relative;
}

.platform-image-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.platform-image {
  max-width: 100%;
  max-height: 180px;
  object-fit: contain;
  border-radius: var(--radius-sm);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform var(--transition-normal);
}

.platform-card:hover .platform-image {
  transform: scale(1.05);
}

/* ===== PLATFORM CONTENT ===== */
.platform-content {
  padding: var(--spacing-2xl);
  flex-grow: 1;
  background-color: var(--content-bg);
  display: flex;
  flex-direction: column;
}

.platform-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.platform-icon {
  font-size: 1.25rem;
  color: var(--primary-blue);
}

.platform-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.6;
  flex-grow: 1;
  margin-bottom: var(--spacing-lg);
}

/* ===== PLATFORM FEATURES ===== */
.platform-features {
  list-style: none;
  padding: 0;
  margin: var(--spacing-lg) 0;
}

.platform-features li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.8125rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.platform-features li::before {
  content: '✓';
  color: var(--success-color);
  font-weight: bold;
  font-size: 0.875rem;
}

/* ===== PLATFORM ACTIONS ===== */
.platform-actions {
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.platform-actions button {
  padding: var(--spacing-md) var(--spacing-2xl);
  font-size: 1rem;
  font-weight: 500;
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.platform-actions button:hover {
  background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 51, 201, 0.3);
}

/* ===== PLATFORM STATUS ===== */
.platform-status {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.platform-status.available {
  background-color: var(--success-bg);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.platform-status.coming-soon {
  background-color: var(--warning-bg);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.platform-status.maintenance {
  background-color: var(--error-bg);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

/* ===== LOADING STATE ===== */
.platform-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.platform-loading-icon {
  font-size: 3rem;
  color: var(--primary-blue);
  animation: platform-select-spin 1s linear infinite;
}

.platform-loading-text {
  font-size: 1rem;
  color: var(--text-secondary);
}

/* ===== EMPTY STATE ===== */
.platform-empty-state {
  text-align: center;
  padding: var(--spacing-3xl);
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
}

.platform-empty-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xl);
}

.platform-empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.platform-empty-description {
  font-size: 1rem;
  color: var(--text-secondary);
  max-width: 400px;
  margin: 0 auto;
}

/* ===== ANIMATIONS ===== */
@keyframes platform-select-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .platform-card {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .platform-content {
  background-color: var(--section-bg);
}

[data-theme="dark"] .platform-image-container {
  background: linear-gradient(135deg, var(--content-bg), var(--section-bg));
  border-color: var(--border-color);
}

[data-theme="dark"] .platform-select-header {
  background-color: var(--section-bg);
}

[data-theme="dark"] .platform-empty-state {
  background-color: var(--section-bg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .platform-select-container {
    padding: var(--spacing-lg);
  }

  .platform-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
  }
  
  .platform-image-container {
    height: 160px;
  }
  
  .platform-image {
    max-height: 140px;
  }
  
  .platform-content {
    padding: var(--spacing-lg);
  }

  .platform-select-header {
    padding: var(--spacing-lg);
  }

  .platform-select-header h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .platform-grid {
    grid-template-columns: 1fr;
  }

  .platform-card {
    margin: 0 var(--spacing-sm);
  }

  .platform-content h3 {
    font-size: 1rem;
  }

  .platform-actions button {
    width: 100%;
    justify-content: center;
  }

  .platform-select-header h2 {
    font-size: 1.25rem;
  }

  .platform-select-header p {
    font-size: 0.875rem;
  }
}
