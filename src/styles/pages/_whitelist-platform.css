/* ===================================
   WHITELIST PLATFORM PAGE STYLES
   ================================= */

/* ===== MAIN CONTAINER ===== */
.whitelist-platform-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  background-color: var(--section-bg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

/* ===== HEADER ===== */
.whitelist-platform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.whitelist-platform-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.whitelist-platform-header-icon {
  font-size: 1.5rem;
  color: var(--primary-blue);
}

/* ===== TABS CONTAINER ===== */
.whitelist-platform-tabs-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.whitelist-platform-tabs-header {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.whitelist-platform-tab-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--text-secondary);
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.whitelist-platform-tab-button:hover {
  color: var(--text-color);
  background-color: var(--hover-bg);
}

.whitelist-platform-tab-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
  background-color: var(--hover-bg);
}

.whitelist-platform-tab-icon {
  font-size: 1.125rem;
}

.whitelist-platform-tab-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
  background-color: var(--content-bg);
}

/* ===== QUERY TAB ===== */
.whitelist-platform-query-tab {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.whitelist-platform-prompt-section,
.whitelist-platform-query-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.whitelist-platform-prompt-section h3,
.whitelist-platform-query-section h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.whitelist-platform-section-icon {
  font-size: 1.125rem;
  color: var(--primary-blue);
}

.whitelist-platform-prompt-input-container {
  display: flex;
  gap: var(--spacing-md);
}

.whitelist-platform-prompt-input {
  flex: 1;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.9375rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.whitelist-platform-prompt-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
}

.whitelist-platform-generate-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0 var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.whitelist-platform-generate-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
  transform: translateY(-1px);
}

.whitelist-platform-generate-button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
  transform: none;
}

.whitelist-platform-query-input-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.whitelist-platform-query-input {
  width: 100%;
  min-height: 120px;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  background-color: var(--input-bg);
  color: var(--text-color);
  resize: vertical;
  transition: all var(--transition-fast);
}

.whitelist-platform-query-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
}

.whitelist-platform-query-actions {
  display: flex;
  gap: var(--spacing-md);
}

.whitelist-platform-run-button,
.whitelist-platform-save-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.whitelist-platform-run-button {
  background: linear-gradient(135deg, var(--success-color), var(--success-color-dark));
  color: white;
}

.whitelist-platform-run-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--success-color-dark), var(--success-color));
  transform: translateY(-1px);
}

.whitelist-platform-save-button {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border: 1px solid var(--border-color);
}

.whitelist-platform-save-button:hover:not(:disabled) {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-1px);
}

.whitelist-platform-run-button:disabled,
.whitelist-platform-save-button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
  transform: none;
  opacity: 0.6;
}

.whitelist-platform-error-message {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--error-bg);
  color: var(--error-text);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.whitelist-platform-error-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

/* ===== QUERY RESULT ===== */
.whitelist-platform-query-result {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--content-bg);
}

.whitelist-platform-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--section-bg);
  border-bottom: 1px solid var(--border-color);
}

.whitelist-platform-result-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.whitelist-platform-result-meta {
  display: flex;
  gap: var(--spacing-lg);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.whitelist-platform-result-table-container {
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.whitelist-platform-result-table {
  width: 100%;
  border-collapse: collapse;
}

.whitelist-platform-result-table th,
.whitelist-platform-result-table td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.whitelist-platform-result-table th {
  background-color: var(--section-bg);
  font-weight: 600;
  color: var(--text-secondary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.whitelist-platform-result-table tr:hover td {
  background-color: var(--hover-bg);
}

/* ===== HISTORY TAB ===== */
.whitelist-platform-history-tab {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.whitelist-platform-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.whitelist-platform-history-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
}

.whitelist-platform-clear-history-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border: none;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.whitelist-platform-clear-history-button:hover {
  background-color: var(--error-color);
  color: white;
  transform: translateY(-1px);
}

.whitelist-platform-empty-state {
  padding: var(--spacing-3xl);
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.9375rem;
  background-color: var(--section-bg);
  border-radius: var(--radius-md);
  border: 2px dashed var(--border-color);
}

.whitelist-platform-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.whitelist-platform-history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.whitelist-platform-history-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background-color: var(--section-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.whitelist-platform-history-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.whitelist-platform-history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.whitelist-platform-history-timestamp {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.whitelist-platform-history-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.whitelist-platform-history-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-sm);
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.whitelist-platform-history-action-button:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-2px);
}

.whitelist-platform-history-query {
  padding: var(--spacing-md);
  background-color: var(--input-bg);
  border-radius: var(--radius-sm);
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-color);
  white-space: pre-wrap;
  overflow-x: auto;
  margin: 0;
  border: 1px solid var(--border-color);
}

.whitelist-platform-history-result-meta {
  display: flex;
  gap: var(--spacing-lg);
  font-size: 0.8125rem;
  color: var(--text-secondary);
}

/* ===== SAVED TAB ===== */
.whitelist-platform-saved-tab {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.whitelist-platform-saved-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.whitelist-platform-saved-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
}

.whitelist-platform-saved-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.whitelist-platform-saved-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background-color: var(--section-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.whitelist-platform-saved-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.whitelist-platform-saved-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.whitelist-platform-saved-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.whitelist-platform-saved-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.whitelist-platform-saved-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-sm);
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.whitelist-platform-saved-action-button:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-2px);
}

.whitelist-platform-saved-query {
  padding: var(--spacing-md);
  background-color: var(--input-bg);
  border-radius: var(--radius-sm);
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-color);
  white-space: pre-wrap;
  overflow-x: auto;
  margin: 0;
  border: 1px solid var(--border-color);
}

.whitelist-platform-saved-timestamp {
  font-size: 0.8125rem;
  color: var(--text-secondary);
}

/* ===== SAVE DIALOG ===== */
.whitelist-platform-save-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.3s ease-in-out;
}

.whitelist-platform-save-dialog-content {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--modal-shadow);
  max-width: 480px;
  width: 90%;
  padding: var(--spacing-2xl);
  border: 1px solid var(--border-color);
}

.whitelist-platform-save-dialog-header {
  margin-bottom: var(--spacing-lg);
}

.whitelist-platform-save-dialog-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.whitelist-platform-save-dialog-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.whitelist-platform-save-dialog-input {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.whitelist-platform-save-dialog-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
}

.whitelist-platform-save-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
}

.whitelist-platform-save-dialog-button {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.whitelist-platform-save-dialog-button.primary {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: white;
}

.whitelist-platform-save-dialog-button.primary:hover {
  background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
  transform: translateY(-1px);
}

.whitelist-platform-save-dialog-button.secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border: 1px solid var(--border-color);
}

.whitelist-platform-save-dialog-button.secondary:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-1px);
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .whitelist-platform-container {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .whitelist-platform-header,
[data-theme="dark"] .whitelist-platform-tabs-header {
  background-color: var(--content-bg);
}

[data-theme="dark"] .whitelist-platform-tab-content {
  background-color: var(--section-bg);
}

[data-theme="dark"] .whitelist-platform-query-result {
  background-color: var(--section-bg);
}

[data-theme="dark"] .whitelist-platform-save-dialog-content {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .whitelist-platform-container {
    height: calc(100vh - 120px);
  }

  .whitelist-platform-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .whitelist-platform-tab-content {
    padding: var(--spacing-lg);
  }

  .whitelist-platform-prompt-input-container,
  .whitelist-platform-query-actions {
    flex-direction: column;
  }

  .whitelist-platform-result-meta,
  .whitelist-platform-history-result-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .whitelist-platform-history-item-header,
  .whitelist-platform-saved-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .whitelist-platform-save-dialog-content {
    margin: var(--spacing-lg);
    max-width: calc(100% - 32px);
  }
}

@media (max-width: 480px) {
  .whitelist-platform-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .whitelist-platform-tab-content {
    padding: var(--spacing-md);
  }

  .whitelist-platform-tabs-header {
    overflow-x: auto;
  }

  .whitelist-platform-tab-button {
    white-space: nowrap;
    min-width: 120px;
  }

  .whitelist-platform-history-actions,
  .whitelist-platform-saved-actions {
    flex-wrap: wrap;
  }

  .whitelist-platform-save-dialog-actions {
    flex-direction: column;
  }
}
