/* ===================================
   ASK AI PAGE STYLES
   ================================= */

/* ===== MAIN CONTAINER ===== */
.ask-ai-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  background-color: var(--section-bg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

/* ===== HEADER ===== */
.ask-ai-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.ask-ai-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.ask-ai-header-icon {
  font-size: 1.5rem;
  color: var(--primary-blue);
}

.ask-ai-clear-conversation-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border: none;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.ask-ai-clear-conversation-btn:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-1px);
}

/* ===== CHAT CONTAINER ===== */
.ask-ai-chat-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.ask-ai-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  background: linear-gradient(180deg, var(--content-bg) 0%, var(--section-bg) 100%);
}

/* ===== MESSAGE STYLES ===== */
.ask-ai-message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  position: relative;
  animation: ask-ai-fadeIn 0.3s ease;
  box-shadow: var(--card-shadow);
}

.ask-ai-user-message {
  align-self: flex-end;
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: white;
  border-bottom-right-radius: var(--radius-xs);
}

.ask-ai-ai-message {
  align-self: flex-start;
  background-color: var(--content-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  border-bottom-left-radius: var(--radius-xs);
}

.ask-ai-message-content {
  font-size: 0.9375rem;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.ask-ai-message-timestamp {
  font-size: 0.6875rem;
  margin-top: var(--spacing-sm);
  align-self: flex-end;
  opacity: 0.7;
  font-weight: 500;
}

/* ===== TYPING INDICATOR ===== */
.ask-ai-typing-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
}

.ask-ai-typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background-color: var(--text-muted);
  animation: ask-ai-typing 1s infinite ease-in-out;
}

.ask-ai-typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.ask-ai-typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.ask-ai-typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

/* ===== INPUT CONTAINER ===== */
.ask-ai-input-container {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-top: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.ask-ai-message-input {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  min-height: 24px;
  max-height: 150px;
  resize: none;
  font-family: inherit;
  font-size: 0.9375rem;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--input-bg);
  transition: all var(--transition-fast);
  overflow-y: auto;
}

.ask-ai-message-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 51, 201, 0.1);
}

.ask-ai-message-input::placeholder {
  color: var(--text-muted);
}

.ask-ai-send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  border: none;
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: white;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--card-shadow);
}

.ask-ai-send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
  transform: scale(1.05);
  box-shadow: var(--card-shadow-hover);
}

.ask-ai-send-button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.6;
}

/* ===== EMPTY STATE ===== */
.ask-ai-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  color: var(--text-secondary);
  padding: var(--spacing-3xl);
}

.ask-ai-empty-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-xl);
  opacity: 0.5;
  color: var(--primary-blue);
}

.ask-ai-empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.ask-ai-empty-description {
  font-size: 1rem;
  max-width: 400px;
  line-height: 1.5;
}

/* ===== ANIMATIONS ===== */
@keyframes ask-ai-fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes ask-ai-typing {
  0%, 100% { 
    transform: translateY(0); 
    opacity: 0.4;
  }
  50% { 
    transform: translateY(-5px); 
    opacity: 1;
  }
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .ask-ai-container {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .ask-ai-header {
  background-color: var(--content-bg);
}

[data-theme="dark"] .ask-ai-messages-container {
  background: linear-gradient(180deg, var(--section-bg) 0%, var(--content-bg) 100%);
}

[data-theme="dark"] .ask-ai-ai-message {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .ask-ai-input-container {
  background-color: var(--content-bg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .ask-ai-container {
    height: calc(100vh - 120px);
  }

  .ask-ai-message {
    max-width: 90%;
  }
  
  .ask-ai-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .ask-ai-header h2 {
    font-size: 1.125rem;
  }
  
  .ask-ai-input-container {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .ask-ai-messages-container {
    padding: var(--spacing-lg);
  }
  
  .ask-ai-clear-conversation-btn span {
    display: none;
  }

  .ask-ai-clear-conversation-btn {
    padding: var(--spacing-sm);
    width: 36px;
    height: 36px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .ask-ai-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .ask-ai-input-container {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .ask-ai-messages-container {
    padding: var(--spacing-md);
  }

  .ask-ai-message {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .ask-ai-message-content {
    font-size: 0.875rem;
  }

  .ask-ai-empty-icon {
    font-size: 3rem;
  }

  .ask-ai-empty-title {
    font-size: 1.125rem;
  }

  .ask-ai-empty-description {
    font-size: 0.875rem;
  }
}
