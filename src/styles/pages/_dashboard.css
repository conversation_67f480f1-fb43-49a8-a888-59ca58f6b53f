/* ===================================
   DASHBOARD PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

/* ===== SUMMARY CARDS ===== */
.dashboard-summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: var(--spacing-xl);
}

.dashboard-summary-card {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--card-shadow);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.dashboard-summary-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow-hover);
}

.dashboard-card-content {
  z-index: 1;
}

.dashboard-card-title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.dashboard-card-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
}

.dashboard-card-icon {
  font-size: 1.5rem;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

/* ===== CARD VARIANTS ===== */
.dashboard-pending-card .dashboard-card-number {
  color: #6366f1;
}

.dashboard-pending-card .dashboard-card-icon {
  background-color: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.dashboard-approved-card .dashboard-card-number {
  color: var(--success-color);
}

.dashboard-approved-card .dashboard-card-icon {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.dashboard-data-sources-card .dashboard-card-number {
  color: var(--info-color);
}

.dashboard-data-sources-card .dashboard-card-icon {
  background-color: var(--info-bg);
  color: var(--info-color);
}

.dashboard-reports-card .dashboard-card-number {
  color: var(--secondary-purple);
}

.dashboard-reports-card .dashboard-card-icon {
  background-color: rgba(139, 92, 246, 0.1);
  color: var(--secondary-purple);
}

/* ===== SECTION CONTAINERS ===== */
.dashboard-section-container {
  background-color: var(--section-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--card-shadow);
}

.dashboard-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: var(--spacing-xl);
  color: var(--text-color);
}

/* ===== RECENT ACTIVITY ===== */
.dashboard-activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.dashboard-activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  background-color: var(--hover-bg);
  transition: background-color var(--transition-fast);
}

.dashboard-activity-item:hover {
  background-color: var(--filter-bg);
}

.dashboard-activity-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1rem;
}

.dashboard-activity-icon.approved {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.dashboard-activity-icon.new {
  background-color: var(--info-bg);
  color: var(--info-color);
}

.dashboard-activity-icon.pending {
  background-color: var(--warning-bg);
  color: var(--warning-color);
}

.dashboard-activity-content {
  flex: 1;
}

.dashboard-activity-title {
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-color);
}

.dashboard-activity-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.dashboard-activity-time {
  color: var(--text-muted);
  font-size: 0.8125rem;
  white-space: nowrap;
}

/* ===== DASHBOARD GRID ===== */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2xl);
}

/* ===== QUICK ACTIONS ===== */
.dashboard-quick-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.dashboard-quick-action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-lg);
  border-radius: var(--radius-md);
  border: none;
  background-color: var(--hover-bg);
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.dashboard-quick-action-btn:hover {
  transform: translateX(5px);
}

.dashboard-quick-action-btn .btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-data-btn:hover {
  background-color: var(--info-bg);
  color: var(--info-color);
}

.dashboard-report-btn:hover {
  background-color: rgba(139, 92, 246, 0.1);
  color: var(--secondary-purple);
}

.dashboard-platform-btn:hover {
  background-color: var(--success-bg);
  color: var(--success-color);
}

/* ===== MY ACCESS SECTION ===== */
.dashboard-access-section {
  margin-bottom: var(--spacing-lg);
}

.dashboard-subsection-title {
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.dashboard-access-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.dashboard-access-tag {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--filter-bg);
  border-radius: var(--radius-xl);
  font-size: 0.8125rem;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.dashboard-access-tag:hover {
  background-color: var(--border-color);
  color: var(--text-color);
}

/* ===== SYSTEM STATUS ===== */
.dashboard-system-status {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.dashboard-status-overview {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.dashboard-status-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
}

.dashboard-status-icon.operational {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.dashboard-status-title {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.dashboard-status-time {
  font-size: 0.8125rem;
  color: var(--text-muted);
}

.dashboard-status-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.dashboard-status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.dashboard-status-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  width: 120px;
}

.dashboard-status-bar {
  flex: 1;
  height: 8px;
  background-color: var(--border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.dashboard-status-progress {
  height: 100%;
  background-color: var(--success-color);
  border-radius: var(--radius-sm);
}

.dashboard-status-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--success-color);
  width: 60px;
  text-align: right;
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .dashboard-summary-card,
[data-theme="dark"] .dashboard-section-container {
  background-color: var(--section-bg);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .dashboard-section-title,
[data-theme="dark"] .dashboard-activity-title,
[data-theme="dark"] .dashboard-status-title {
  color: var(--text-color);
}

[data-theme="dark"] .dashboard-card-title,
[data-theme="dark"] .dashboard-activity-description,
[data-theme="dark"] .dashboard-subsection-title,
[data-theme="dark"] .dashboard-status-label {
  color: var(--text-secondary);
}

[data-theme="dark"] .dashboard-activity-item {
  background-color: var(--content-bg);
}

[data-theme="dark"] .dashboard-activity-item:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .dashboard-quick-action-btn {
  background-color: var(--input-bg);
  color: var(--text-color);
}

[data-theme="dark"] .dashboard-access-tag {
  background-color: var(--input-bg);
  color: var(--text-secondary);
}

[data-theme="dark"] .dashboard-access-tag:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

[data-theme="dark"] .dashboard-status-bar {
  background-color: var(--input-bg);
}

[data-theme="dark"] .dashboard-status-overview {
  border-bottom-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    gap: var(--spacing-xl);
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-section-container {
    padding: var(--spacing-lg);
  }

  .dashboard-activity-item {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }

  .dashboard-quick-action-btn {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .dashboard-summary-cards {
    grid-template-columns: 1fr;
  }

  .dashboard-activity-item {
    flex-direction: column;
    align-items: stretch;
  }

  .dashboard-activity-time {
    align-self: flex-end;
    margin-top: var(--spacing-sm);
  }

  .dashboard-status-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .dashboard-status-label {
    width: auto;
  }

  .dashboard-status-value {
    width: auto;
    text-align: left;
  }

  .dashboard-card-title {
    font-size: 0.875rem;
  }

  .dashboard-card-number {
    font-size: 1.75rem;
  }

  .dashboard-card-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
}
