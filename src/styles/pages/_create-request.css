/* ===================================
   CREATE REQUEST PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.create-request-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* ===== PAGE HEADER ===== */
.create-request-header {
  text-align: center;
  margin-bottom: 40px;
}

.create-request-header h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.create-request-header p {
  font-size: 1rem;
  color: var(--text-secondary);
}

/* ===== REQUEST TYPE GRID ===== */
.create-request-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-2xl);
}

.create-request-type-card {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all var(--transition-normal);
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
}

.create-request-type-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow-hover);
}

.create-request-type-card:active {
  transform: scale(0.98);
}

/* ===== CARD ICON SECTION ===== */
.create-request-type-icon {
  padding: var(--spacing-3xl) 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all var(--transition-normal);
}

.create-request-icon-container {
  background-color: var(--content-bg);
  width: 84px;
  height: 84px;
  border-radius: var(--radius-full);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: var(--card-shadow);
}

/* ===== CARD CONTENT ===== */
.create-request-type-content {
  padding: var(--spacing-2xl);
  flex-grow: 1;
  background-color: var(--content-bg);
  border-top: 1px solid var(--border-color);
}

.create-request-type-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.create-request-type-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* ===== REQUEST FORM CONTAINER ===== */
.create-request-page-container {
  max-width: 900px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.create-request-form-container {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-3xl);
  margin-bottom: var(--spacing-3xl);
  position: relative;
}

.create-request-form-container h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-2xl);
  text-align: center;
}

/* ===== FORM ELEMENTS ===== */
.create-request-form-group {
  margin-bottom: var(--spacing-xl);
}

.create-request-form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.create-request-form-group input[type="text"],
.create-request-form-group input[type="date"],
.create-request-form-group textarea,
.create-request-form-group select {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: border-color var(--transition-normal), box-shadow var(--transition-normal);
}

.create-request-form-group input[type="text"]:focus,
.create-request-form-group input[type="date"]:focus,
.create-request-form-group textarea:focus,
.create-request-form-group select:focus {
  border-color: var(--success-color);
  box-shadow: 0 0 0 2px rgba(0, 207, 106, 0.2);
  outline: none;
}

.create-request-form-group textarea {
  min-height: 120px;
  resize: vertical;
}

/* ===== FORM ACTIONS ===== */
.create-request-form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-3xl);
  gap: var(--spacing-lg);
}

.create-request-btn-primary,
.create-request-btn-secondary {
  padding: var(--spacing-md) var(--spacing-2xl);
  border-radius: var(--radius-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  font-size: 1rem;
  flex: 1;
}

.create-request-btn-primary {
  background-color: var(--success-color);
  color: white;
}

.create-request-btn-primary:hover {
  background-color: var(--success-color-dark);
  transform: translateY(-2px);
}

.create-request-btn-primary:disabled {
  background-color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
}

.create-request-btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border: 1px solid var(--border-color);
}

.create-request-btn-secondary:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-2px);
}

/* ===== ERROR MESSAGE ===== */
.create-request-error-message {
  background-color: var(--error-bg);
  color: var(--error-text);
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xl);
  border: 1px solid var(--error-color);
}

/* ===== SUCCESS MODAL ===== */
.create-request-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal);
}

.create-request-modal-content {
  background-color: var(--content-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-3xl);
  width: 90%;
  max-width: 500px;
  box-shadow: var(--modal-shadow);
  position: relative;
}

.create-request-success-modal h3 {
  color: var(--success-color);
  text-align: center;
  margin-bottom: var(--spacing-lg);
  font-size: 1.375rem;
}

.create-request-success-modal p {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  color: var(--text-secondary);
}

.create-request-details {
  padding: var(--spacing-lg);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xl);
  background-color: var(--hover-bg);
  border: 1px solid var(--border-color);
}

.create-request-details p {
  text-align: left;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.create-request-success-modal button {
  display: block;
  margin: 0 auto;
}

/* ===== ANIMATIONS ===== */
@keyframes create-request-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
  }
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .create-request-type-card {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .create-request-type-content {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .create-request-icon-container {
  background-color: var(--content-bg);
}

[data-theme="dark"] .create-request-form-container {
  background-color: var(--section-bg);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .create-request-modal-content {
  background-color: var(--section-bg);
}

[data-theme="dark"] .create-request-details {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .create-request-container {
    padding: var(--spacing-lg);
  }

  .create-request-type-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--spacing-lg);
  }

  .create-request-type-icon {
    padding: var(--spacing-2xl) 0;
  }

  .create-request-icon-container {
    width: 72px;
    height: 72px;
  }

  .create-request-type-content {
    padding: var(--spacing-lg);
  }

  .create-request-form-container {
    padding: var(--spacing-xl);
  }

  .create-request-form-actions {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .create-request-btn-primary,
  .create-request-btn-secondary {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .create-request-container {
    padding: var(--spacing-md);
  }

  .create-request-page-container {
    padding: var(--spacing-md);
  }

  .create-request-form-container {
    padding: var(--spacing-lg);
  }

  .create-request-modal-content {
    padding: var(--spacing-xl);
  }
}
