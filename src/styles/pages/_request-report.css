/* ===================================
   REQUEST REPORT PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.request-report-container {
  width: 100%;
}

.request-report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  gap: var(--spacing-lg);
}

.request-report-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  color: var(--text-color);
}

.request-report-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* ===== SECTIONS ===== */
.request-report-section {
  margin-bottom: var(--spacing-3xl);
}

.request-report-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.request-report-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-color);
}

/* ===== TABS ===== */
.request-report-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  border-bottom: 1px solid var(--border-color);
}

.tab-buttons {
  display: flex;
}

.tab-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-right: var(--spacing-sm);
}

.tab-actions .btn {
  height: 40px;
}

.tab-actions .btn-primary {
  padding: 0 var(--spacing-xl);
}

/* Custom button styles for tab actions */
.tab-actions button[title="Clear All Selections"] {
  background-color: var(--btn-secondary-bg);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

.tab-actions button[title="Clear All Selections"]:hover:not(:disabled) {
  background-color: var(--btn-secondary-hover);
  border-color: var(--text-muted);
  color: var(--text-color);
}

.tab-actions button[title="Clear All Selections"]:disabled {
  background-color: var(--btn-secondary-bg);
  border-color: var(--border-color);
  color: var(--text-muted);
  cursor: not-allowed;
}

.tab-actions button[title="Refresh Page"] {
  background-color: var(--info-bg);
  border-color: var(--info-color);
  color: var(--info-color);
}

.tab-actions button[title="Refresh Page"]:hover {
  background-color: rgba(59, 130, 246, 0.2);
  border-color: var(--info-color);
  color: var(--info-color);
}

/* Tab button styles */
.tab-button {
  padding: var(--spacing-md) var(--spacing-xl);
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: all var(--transition-fast);
}

.tab-button:hover {
  color: var(--text-color);
  background-color: var(--hover-bg);
}

.tab-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
}

/* ===== SEARCH AND FILTER ===== */
.filter-search-bar {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 200px;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.search-icon.spinning {
  animation: spinner 1s linear infinite;
}

.clear-search-icon {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.clear-search-icon:hover {
  color: var(--text-color);
  background-color: var(--hover-bg);
}

.search-input {
  width: 100%;
  padding: var(--spacing-md) 40px var(--spacing-md) 36px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  flex: 1;
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.clear-search {
  position: absolute;
  right: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.clear-search:hover {
  background-color: var(--hover-bg);
}

.filter-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.filter-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 0.875rem;
  width: 200px;
  transition: all var(--transition-fast);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.clear-filters-btn {
  background: none;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.8125rem;
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  white-space: nowrap;
  flex-shrink: 0;
  height: 38px;
  transition: all var(--transition-fast);
}

.clear-filters-btn:hover {
  background-color: var(--hover-bg);
}

/* ===== INFINITE SCROLL ===== */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
  gap: var(--spacing-md);
  color: var(--text-secondary);
}

.loading-spinner-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-left: var(--spacing-sm);
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-blue);
  border-radius: var(--radius-full);
  animation: spinner 0.8s linear infinite;
}

.scroll-sentinel {
  height: 50px;
  width: 100%;
  margin-top: var(--spacing-xl);
  position: relative;
  z-index: 1;
}

/* ===== REPORT GRID ===== */
.request-report-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.report-item {
  background-color: var(--section-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
}

.report-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--card-shadow-hover);
}

.report-item.selected {
  border-color: var(--primary-blue);
}

.report-item-thumbnail {
  width: 100%;
  height: 150px;
  background-color: var(--hover-bg);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  overflow: hidden;
}

.report-item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.report-item-thumbnail .placeholder-icon {
  font-size: 3rem;
  color: var(--text-muted);
}

.report-item-content {
  padding: var(--spacing-lg);
}

.report-item-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.report-item-owner {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.report-item-updated {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.report-item-selection {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background-color: var(--content-bg);
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-fast);
}

.report-item-selection.selected {
  background-color: var(--primary-blue);
  color: white;
}

/* ===== ERROR AND SUCCESS MESSAGES ===== */
.error-message {
  background-color: var(--error-bg);
  border: 1px solid var(--error-color);
  color: var(--error-text);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.success-message {
  position: fixed;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  background-color: var(--success-color);
  color: white;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-sm);
  box-shadow: var(--modal-shadow);
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  animation: fadeIn 0.3s ease-in-out, fadeOut 0.3s ease-in-out 2.7s;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-3xl);
  background-color: var(--section-bg);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
}

/* ===== MODAL STYLES ===== */
.request-report-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal-backdrop);
}

.request-report-modal-container {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--modal-shadow);
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalFadeIn 0.3s ease;
}

.request-report-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-2xl);
  border-bottom: 1px solid var(--border-color);
}

.request-report-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.request-report-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.request-report-modal-close:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

.request-report-modal-body {
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-xs) var(--spacing-2xl);
}

.request-report-modal-actions {
  display: flex;
  justify-content: space-between;
  padding: 0 var(--spacing-2xl) var(--spacing-2xl);
}

.request-report-modal-actions-left {
  display: flex;
  gap: var(--spacing-md);
}

.request-report-modal-actions-right {
  display: flex;
  gap: var(--spacing-md);
}

/* ===== SELECTED ITEMS SUMMARY ===== */
.selected-items-summary {
  background-color: var(--section-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-sm);
  max-height: 300px;
  overflow-y: auto;
}

.selected-items-header {
  margin: var(--spacing-md) 0 var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--text-color);
}

.selected-items-header:first-child {
  margin-top: 0;
}

.selected-items-list {
  margin-bottom: var(--spacing-lg);
}

.selected-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
  background-color: var(--content-bg);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.selected-item:hover {
  background-color: var(--hover-bg);
}

.selected-item-name {
  font-weight: 500;
  color: var(--text-color);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: var(--spacing-md);
}

.selected-item-owner {
  color: var(--text-secondary);
  font-size: 0.8125rem;
}

.no-items-selected {
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-sm) 0;
}

.form-icon {
  margin-right: var(--spacing-sm);
  vertical-align: middle;
  color: var(--text-secondary);
}

/* ===== SUCCESS MODAL STYLES ===== */
.success-modal {
  max-width: 500px;
}

.success-modal-body {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-2xl) var(--spacing-3xl);
}

.success-modal-icon {
  font-size: 3.75rem;
  color: var(--success-color);
  margin-bottom: var(--spacing-xl);
}

.success-modal-message {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
}

.success-modal-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0;
}

.request-details {
  background-color: var(--hover-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  text-align: left;
}

.request-details p {
  margin: var(--spacing-sm) 0;
  font-size: 0.875rem;
  color: var(--text-color);
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .loading-spinner-small {
  border-color: var(--border-color);
  border-top-color: var(--primary-blue);
}

[data-theme="dark"] .tab-actions button[title="Clear All Selections"] {
  background-color: #262626;
  border-color: #434343;
  color: #d9d9d9;
}

[data-theme="dark"] .tab-actions button[title="Clear All Selections"]:hover:not(:disabled) {
  background-color: #303030;
  border-color: #595959;
  color: #f5f5f5;
}

[data-theme="dark"] .tab-actions button[title="Clear All Selections"]:disabled {
  background-color: #262626;
  border-color: #434343;
  color: #595959;
}

[data-theme="dark"] .tab-actions button[title="Refresh Page"] {
  background-color: #111d2c;
  border-color: #153450;
  color: #40a9ff;
}

[data-theme="dark"] .tab-actions button[title="Refresh Page"]:hover {
  background-color: #112a45;
  border-color: #15395b;
  color: #69c0ff;
}

[data-theme="dark"] .tab-button {
  color: var(--text-secondary);
}

[data-theme="dark"] .tab-button:hover {
  color: var(--text-color);
  background-color: var(--hover-bg);
}

[data-theme="dark"] .tab-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
}

[data-theme="dark"] .search-input,
[data-theme="dark"] .filter-select {
  background-color: var(--input-bg);
  color: var(--text-color);
  border-color: var(--border-color);
}

[data-theme="dark"] .search-icon,
[data-theme="dark"] .clear-search,
[data-theme="dark"] .clear-search-icon {
  color: var(--text-secondary);
}

[data-theme="dark"] .clear-search-icon:hover {
  color: var(--text-color);
  background-color: var(--hover-bg);
}

[data-theme="dark"] .clear-filters-btn {
  color: var(--text-color);
  border-color: var(--border-color);
}

[data-theme="dark"] .clear-filters-btn:hover,
[data-theme="dark"] .clear-search:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .loading-more {
  color: var(--text-secondary);
}

[data-theme="dark"] .request-report-modal-container {
  background-color: var(--section-bg);
}

[data-theme="dark"] .request-report-modal-header {
  border-color: var(--border-color);
}

[data-theme="dark"] .request-report-modal-header h3 {
  color: var(--text-color);
}

[data-theme="dark"] .request-report-modal-close {
  color: var(--text-secondary);
}

[data-theme="dark"] .request-report-modal-close:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

[data-theme="dark"] .selected-items-summary {
  background-color: var(--content-bg);
}

[data-theme="dark"] .selected-items-header {
  border-color: var(--border-color);
}

[data-theme="dark"] .selected-item {
  background-color: var(--section-bg);
}

[data-theme="dark"] .selected-item:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .selected-item-name {
  color: var(--text-color);
}

[data-theme="dark"] .selected-item-owner {
  color: var(--text-secondary);
}

[data-theme="dark"] .report-item-updated {
  color: var(--text-secondary);
}

[data-theme="dark"] .no-items-selected {
  color: var(--text-secondary);
}

[data-theme="dark"] .form-icon {
  color: var(--text-secondary);
}

[data-theme="dark"] .success-modal-icon {
  color: var(--success-color);
}

[data-theme="dark"] .success-modal-message {
  color: var(--text-color);
}

[data-theme="dark"] .success-modal-description {
  color: var(--text-secondary);
}

[data-theme="dark"] .request-details {
  background-color: var(--section-bg);
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .request-details p {
  color: var(--text-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .request-report-grid {
    grid-template-columns: 1fr;
  }

  .request-report-header {
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }

  .request-report-title {
    order: 1;
    width: 100%;
    margin-top: var(--spacing-sm);
  }

  .back-button {
    order: 0;
  }

  .request-report-actions {
    order: 2;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    justify-content: center;
  }

  .filter-search-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .clear-filters-btn {
    margin-top: var(--spacing-sm);
    width: 100%;
    justify-content: center;
    margin-left: 0;
  }

  .filter-container {
    justify-content: space-between;
  }

  .request-report-tabs {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
  }

  .tab-buttons {
    overflow-x: auto;
    white-space: nowrap;
  }

  .tab-actions {
    margin-right: 0;
    width: 100%;
    gap: var(--spacing-md);
  }

  .tab-actions .btn {
    flex: 1;
  }

  .request-report-modal-container {
    width: 95%;
    max-height: 95vh;
  }

  .request-report-modal-header,
  .request-report-modal-body,
  .request-report-modal-actions {
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }

  .request-report-modal-actions {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .request-report-modal-actions-left,
  .request-report-modal-actions-right {
    width: 100%;
    justify-content: center;
  }

  .request-report-modal-actions .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .request-report-container {
    padding: var(--spacing-md);
  }

  .request-report-header {
    margin-bottom: var(--spacing-lg);
  }

  .request-report-title {
    font-size: 1.25rem;
  }

  .tab-button {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
  }

  .filter-search-bar {
    gap: var(--spacing-sm);
  }

  .search-input {
    font-size: 1rem; /* Prevent zoom on iOS */
  }

  .filter-select {
    width: 100%;
  }

  .report-item-content {
    padding: var(--spacing-md);
  }

  .report-item-title {
    font-size: 0.875rem;
  }

  .report-item-owner,
  .report-item-updated {
    font-size: 0.8125rem;
  }

  .success-message {
    top: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .request-report-modal-container {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }

  .selected-items-summary {
    max-height: 200px;
  }
}
