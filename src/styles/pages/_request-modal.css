/* ===================================
   REQUEST MODAL STYLES
   ================================= */

/* ===== REQUEST SUCCESS MODAL ===== */
.request-success-modal .modal-container {
  max-width: 500px;
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--modal-shadow);
  overflow: hidden;
  animation: modalFadeIn 0.3s ease;
}

.request-success-modal .modal-header {
  background: linear-gradient(135deg, var(--success-color), var(--success-color-dark));
  color: white;
  padding: var(--spacing-xl) var(--spacing-2xl);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  text-align: center;
  position: relative;
}

.request-success-modal .modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="1s"/></circle><circle cx="60" cy="70" r="1" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite" begin="0.5s"/></circle></svg>');
  opacity: 0.3;
}

.request-success-modal .modal-header h3 {
  color: white;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.request-success-modal .modal-body {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-2xl);
  background-color: var(--content-bg);
}

/* ===== SUCCESS ICON ===== */
.request-success-modal .success-icon {
  font-size: 3rem;
  color: var(--success-color);
  margin-bottom: var(--spacing-xl);
  display: inline-block;
  animation: request-modal-bounceIn 0.6s ease;
}

.request-success-modal .success-message {
  font-size: 1.125rem;
  color: var(--text-color);
  margin-bottom: var(--spacing-xl);
  font-weight: 500;
  line-height: 1.4;
}

/* ===== DETAIL ROWS ===== */
.request-success-modal .request-details {
  background-color: var(--section-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-xl);
  margin: var(--spacing-xl) 0;
  text-align: left;
  border: 1px solid var(--border-color);
}

.request-success-modal .detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.request-success-modal .detail-row:hover {
  background-color: var(--hover-bg);
  margin: 0 calc(-1 * var(--spacing-md));
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
  border-radius: var(--radius-sm);
}

.request-success-modal .detail-row:last-child {
  border-bottom: none;
}

.request-success-modal .detail-label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.request-success-modal .detail-value {
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.875rem;
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

/* ===== MODAL ACTIONS ===== */
.request-modal-actions {
  padding: 0 var(--spacing-2xl) var(--spacing-2xl);
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.request-modal-actions .btn {
  min-width: 120px;
  padding: var(--spacing-md) var(--spacing-xl);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.request-modal-actions .btn-primary {
  background: linear-gradient(135deg, var(--success-color), var(--success-color-dark));
  border: none;
  color: white;
}

.request-modal-actions .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 207, 106, 0.3);
}

.request-modal-actions .btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border: 1px solid var(--border-color);
}

.request-modal-actions .btn-secondary:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-2px);
}

/* ===== CONFIRMATION MODAL ===== */
.request-confirmation-modal .modal-container {
  max-width: 450px;
}

.request-confirmation-modal .modal-header {
  background: linear-gradient(135deg, var(--warning-color), var(--warning-color-dark));
  color: white;
}

.request-confirmation-modal .confirmation-icon {
  font-size: 2.5rem;
  color: var(--warning-color);
  margin-bottom: var(--spacing-lg);
}

.request-confirmation-modal .confirmation-message {
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: var(--spacing-xl);
  line-height: 1.5;
}

.request-confirmation-modal .confirmation-details {
  background-color: var(--warning-bg);
  border: 1px solid var(--warning-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  font-size: 0.875rem;
  color: var(--warning-text);
}

/* ===== ERROR MODAL ===== */
.request-error-modal .modal-container {
  max-width: 450px;
}

.request-error-modal .modal-header {
  background: linear-gradient(135deg, var(--error-color), var(--error-color-dark));
  color: white;
}

.request-error-modal .error-icon {
  font-size: 2.5rem;
  color: var(--error-color);
  margin-bottom: var(--spacing-lg);
}

.request-error-modal .error-message {
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: var(--spacing-xl);
  line-height: 1.5;
}

.request-error-modal .error-details {
  background-color: var(--error-bg);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  font-size: 0.875rem;
  color: var(--error-text);
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

/* ===== ANIMATIONS ===== */
@keyframes request-modal-bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .request-success-modal .modal-header {
  background: linear-gradient(135deg, #2d5a2d, #1e3a1e);
}

[data-theme="dark"] .request-success-modal .success-message,
[data-theme="dark"] .request-confirmation-modal .confirmation-message,
[data-theme="dark"] .request-error-modal .error-message {
  color: var(--text-color);
}

[data-theme="dark"] .request-success-modal .request-details {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .request-success-modal .detail-row {
  border-color: var(--border-color);
}

[data-theme="dark"] .request-success-modal .detail-row:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .request-success-modal .detail-label {
  color: var(--text-secondary);
}

[data-theme="dark"] .request-success-modal .detail-value {
  color: var(--text-color);
}

[data-theme="dark"] .request-confirmation-modal .modal-header {
  background: linear-gradient(135deg, #8b5a00, #5c3a00);
}

[data-theme="dark"] .request-error-modal .modal-header {
  background: linear-gradient(135deg, #7f1d1d, #5c1515);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .request-success-modal .modal-container,
  .request-confirmation-modal .modal-container,
  .request-error-modal .modal-container {
    margin: var(--spacing-lg);
    max-width: calc(100% - 32px);
  }

  .request-success-modal .modal-header,
  .request-success-modal .modal-body,
  .request-modal-actions {
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }

  .request-success-modal .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .request-success-modal .detail-value {
    text-align: left;
    max-width: 100%;
  }

  .request-modal-actions {
    flex-direction: column;
  }

  .request-modal-actions .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .request-success-modal .success-icon,
  .request-confirmation-modal .confirmation-icon,
  .request-error-modal .error-icon {
    font-size: 2rem;
  }

  .request-success-modal .success-message,
  .request-confirmation-modal .confirmation-message,
  .request-error-modal .error-message {
    font-size: 1rem;
  }
}
