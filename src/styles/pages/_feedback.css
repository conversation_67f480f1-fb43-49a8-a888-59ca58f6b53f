/* ===================================
   FEEDBACK PAGE STYLES
   ================================= */

/* ===== FEEDBACK LIST CONTAINER ===== */
.feedback-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.feedback-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
  padding-bottom: var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
}

.feedback-add-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-green), var(--primary-green-dark));
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.feedback-add-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 207, 106, 0.3);
}

/* ===== CONTENT STATES ===== */
.feedback-list-content {
  min-height: 400px;
}

.feedback-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3xl) var(--spacing-xl);
  color: var(--text-muted);
}

.feedback-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px var(--spacing-xl);
  text-align: center;
  color: var(--text-muted);
}

.feedback-empty-icon {
  margin-bottom: var(--spacing-xl);
  color: var(--border-color);
  font-size: 3rem;
}

.feedback-empty-state h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1.25rem;
  color: var(--text-color);
}

.feedback-empty-state p {
  margin: 0 0 var(--spacing-2xl) 0;
  font-size: 1rem;
  max-width: 400px;
}

.feedback-empty-action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-2xl);
  background: linear-gradient(135deg, var(--primary-green), var(--primary-green-dark));
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.feedback-empty-action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 207, 106, 0.3);
}

/* ===== FEEDBACK GRID ===== */
.feedback-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-xl);
}

.feedback-card {
  background-color: var(--content-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  transition: all var(--transition-fast);
  box-shadow: var(--card-shadow);
  cursor: pointer;
}

.feedback-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

/* ===== CARD HEADER ===== */
.feedback-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.feedback-title h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  line-height: 1.3;
}

.feedback-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 0.75rem;
}

.feedback-id {
  color: var(--text-muted);
  font-weight: 500;
}

.feedback-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 500;
  text-transform: capitalize;
}

.feedback-priority-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 0.6875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.feedback-priority-badge.high {
  background-color: var(--error-bg);
  color: var(--error-color);
}

.feedback-priority-badge.medium {
  background-color: var(--warning-bg);
  color: var(--warning-color);
}

.feedback-priority-badge.low {
  background-color: var(--info-bg);
  color: var(--info-color);
}

/* ===== CARD BODY ===== */
.feedback-card-body {
  margin-bottom: var(--spacing-lg);
}

.feedback-description {
  color: var(--text-color);
  line-height: 1.5;
  max-height: 100px;
  overflow: hidden;
  position: relative;
}

.feedback-description::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(transparent, var(--content-bg));
}

/* ===== CARD FOOTER ===== */
.feedback-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.feedback-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feedback-detail {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--text-muted);
}

.feedback-attachments {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.feedback-attachment-count {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.feedback-retry-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-green);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-left: auto;
}

.feedback-retry-button:hover {
  background-color: var(--primary-green-dark);
}

/* ===== SEND FEEDBACK FORM ===== */
.feedback-container {
  max-width: 800px;
  margin: 0 auto;
}

.feedback-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
  padding-bottom: var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
}

.feedback-header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex: 1;
}

.feedback-header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-green), var(--primary-green-dark));
  border-radius: var(--radius-lg);
  color: white;
  font-size: 1.5rem;
}

.feedback-header-text h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color);
}

.feedback-header-text p {
  margin: var(--spacing-xs) 0 0 0;
  font-size: 1rem;
  color: var(--text-muted);
}

/* ===== FORM CONTAINER ===== */
.feedback-form-container {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-3xl);
  border: 1px solid var(--border-color);
}

.feedback-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.feedback-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feedback-form-group label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.875rem;
}

.feedback-form-group input,
.feedback-form-group select,
.feedback-form-group textarea {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.feedback-form-group input:focus,
.feedback-form-group select:focus,
.feedback-form-group textarea:focus {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(0, 207, 106, 0.1);
  outline: none;
}

.feedback-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

/* ===== DESCRIPTION EDITOR ===== */
.feedback-description-editor {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--input-bg);
}

.feedback-description-editor .ql-toolbar {
  border: none;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.feedback-description-editor .ql-container {
  border: none;
  min-height: 120px;
  font-size: 0.875rem;
}

/* ===== ATTACHMENT SECTION ===== */
.feedback-attachment-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.feedback-attachment-error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--error-bg);
  color: var(--error-text);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-sm);
  font-size: 0.8125rem;
  line-height: 1.4;
  animation: fadeIn 0.3s ease-in-out;
}

.feedback-attachment-error-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

.feedback-attachment-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
  width: fit-content;
}

.feedback-attachment-button:hover {
  border-color: var(--primary-green);
  color: var(--primary-green);
  background-color: rgba(0, 207, 106, 0.05);
}

.feedback-attachments-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feedback-attachment-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: var(--section-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.8125rem;
  position: relative;
}

.feedback-attachment-item.uploading {
  border-color: var(--primary-green);
  background-color: rgba(0, 207, 106, 0.05);
}

.feedback-attachment-item.uploaded {
  border-color: var(--success-color);
  background-color: var(--success-bg);
}

.feedback-attachment-item.error {
  border-color: var(--error-color);
  background-color: var(--error-bg);
}

.feedback-attachment-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
  padding-right: 32px;
}

.feedback-attachment-name {
  flex: 1;
  color: var(--text-color);
  font-weight: 500;
}

.feedback-attachment-size {
  color: var(--text-muted);
}

.feedback-upload-progress {
  color: var(--primary-green);
  font-weight: 500;
  font-size: 0.75rem;
}

.feedback-upload-status {
  font-weight: 500;
  font-size: 0.75rem;
}

.feedback-upload-status.success {
  color: var(--success-color);
}

.feedback-upload-status.error {
  color: var(--error-color);
}

.feedback-progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-top: var(--spacing-xs);
}

.feedback-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green), var(--primary-green-dark));
  border-radius: var(--radius-sm);
  transition: width var(--transition-normal);
}

.feedback-file-error-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-sm);
  margin-top: var(--spacing-xs);
}

.feedback-error-text {
  flex: 1;
  color: var(--error-color);
  font-size: 0.75rem;
}

/* ===== REMOVE BUTTON ===== */
.feedback-remove-attachment {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 1rem;
  padding: var(--spacing-xs);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.feedback-remove-attachment:hover {
  background-color: var(--error-bg);
  color: var(--error-color);
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .feedback-card {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .feedback-form-container {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .feedback-description::after {
  background: linear-gradient(transparent, var(--section-bg));
}

[data-theme="dark"] .feedback-description-editor {
  background-color: var(--input-bg);
}

[data-theme="dark"] .feedback-description-editor .ql-toolbar {
  background-color: var(--content-bg);
}

[data-theme="dark"] .feedback-attachment-item {
  background-color: var(--content-bg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .feedback-list-container,
  .feedback-container {
    padding: var(--spacing-lg);
  }

  .feedback-grid {
    grid-template-columns: 1fr;
  }

  .feedback-list-header,
  .feedback-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
  }

  .feedback-form-row {
    grid-template-columns: 1fr;
  }

  .feedback-form-container {
    padding: var(--spacing-xl);
  }

  .feedback-card {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .feedback-list-container,
  .feedback-container {
    padding: var(--spacing-md);
  }

  .feedback-form-container {
    padding: var(--spacing-lg);
  }

  .feedback-header-content {
    flex-direction: column;
    text-align: center;
  }

  .feedback-header-icon {
    align-self: center;
  }

  .feedback-attachment-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding-right: var(--spacing-md);
  }
}
