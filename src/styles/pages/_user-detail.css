/* ===================================
   USER DETAIL PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.user-detail-container {
  margin: 0 auto;
  padding: var(--spacing-xl);
  max-width: 1200px;
  background-color: var(--bg-color);
}

/* ===== HEADER ===== */
.user-detail-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-xl);
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
}

.user-detail-back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--hover-bg);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.user-detail-back-button:hover {
  color: var(--text-color);
  background: var(--btn-secondary-hover);
  transform: translateY(-1px);
}

.user-detail-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
}

/* ===== CONTENT LAYOUT ===== */
.user-detail-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* ===== PLATFORM SECTIONS ===== */
.platform-section {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: all var(--transition-normal);
  background-color: var(--content-bg);
}

.platform-section:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.platform-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--section-bg);
  padding: var(--spacing-lg) var(--spacing-xl);
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  position: relative;
}

.platform-title-bar::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, var(--primary-blue), var(--secondary-purple));
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.platform-title-bar:hover::before {
  opacity: 1;
}

.platform-title-bar:hover {
  background-color: var(--hover-bg);
}

.platform-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.platform-icon {
  font-size: 1.25rem;
  color: var(--primary-blue);
}

.toggle-icon {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.toggle-icon:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

.toggle-icon svg {
  width: 20px;
  height: 20px;
  transition: transform var(--transition-fast);
}

.platform-section.expanded .toggle-icon svg {
  transform: rotate(180deg);
}

/* ===== USER INFO TABLE ===== */
.user-info-table {
  padding: var(--spacing-xl);
  animation: user-detail-fadeIn 0.3s ease;
}

.user-info-table table {
  width: 100%;
  border-collapse: collapse;
}

.user-info-table tr {
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.user-info-table tr:hover {
  background-color: var(--hover-bg);
}

.user-info-table tr:last-child {
  border-bottom: none;
}

.user-info-table td {
  padding: var(--spacing-lg) 0;
  vertical-align: top;
}

.info-label {
  width: 30%;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: capitalize;
  font-size: 0.875rem;
  padding-right: var(--spacing-lg);
}

.user-info-value {
  width: 70%;
  color: var(--text-color);
  font-size: 0.875rem;
  line-height: 1.4;
}

/* ===== ARRAY VALUES ===== */
.array-values-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.array-value-item {
  display: inline-block;
  background-color: var(--filter-bg);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  color: var(--text-color);
  font-weight: 500;
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.array-value-item:hover {
  background-color: var(--primary-blue);
  color: white;
  transform: translateY(-1px);
}

/* ===== PASSWORD DISPLAY ===== */
.password-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.password-value {
  font-family: monospace;
  background-color: var(--input-bg);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  font-size: 0.8125rem;
  letter-spacing: 1px;
}

.password-toggle-btn {
  background-color: var(--btn-secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  font-size: 0.75rem;
  white-space: nowrap;
  transition: all var(--transition-fast);
  color: var(--text-color);
  font-weight: 500;
}

.password-toggle-btn:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-1px);
}

/* ===== LOADING AND ERROR STATES ===== */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3xl);
  font-size: 1.125rem;
  color: var(--text-secondary);
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
}

.loading-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-lg);
  color: var(--primary-blue);
  animation: user-detail-spin 1s linear infinite;
}

.error-container {
  text-align: center;
  padding: var(--spacing-3xl);
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
}

.error-message {
  color: var(--error-text);
  margin-bottom: var(--spacing-xl);
  font-size: 1rem;
  padding: var(--spacing-lg);
  background-color: var(--error-bg);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--error-color);
  line-height: 1.5;
}

.error-icon {
  font-size: 2rem;
  color: var(--error-color);
  margin-bottom: var(--spacing-lg);
}

.retry-button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--primary-blue);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.retry-button:hover {
  background-color: var(--primary-blue-dark);
  transform: translateY(-1px);
}

/* ===== ANIMATIONS ===== */
@keyframes user-detail-fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(10px);
  }
  to { 
    opacity: 1; 
    transform: translateY(0);
  }
}

@keyframes user-detail-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .platform-section {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .platform-title-bar {
  background-color: var(--content-bg);
}

[data-theme="dark"] .platform-title-bar:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .user-info-table tr:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .array-value-item {
  background-color: var(--input-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .password-value {
  background-color: var(--input-bg);
  border-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .user-detail-container {
    padding: var(--spacing-lg);
  }

  .user-detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-lg);
  }

  .user-detail-header h2 {
    font-size: 1.25rem;
  }

  .platform-title-bar {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .user-info-table {
    padding: var(--spacing-lg);
  }

  .user-info-table table,
  .user-info-table tr,
  .user-info-table td {
    display: block;
  }

  .info-label {
    width: 100%;
    padding-right: 0;
    padding-bottom: var(--spacing-xs);
    font-weight: 700;
    color: var(--text-color);
  }

  .user-info-value {
    width: 100%;
    padding-bottom: var(--spacing-lg);
  }

  .password-display {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .user-detail-container {
    padding: var(--spacing-md);
  }

  .platform-title {
    font-size: 1rem;
  }

  .array-values-container {
    gap: var(--spacing-xs);
  }

  .array-value-item {
    font-size: 0.6875rem;
    padding: 2px var(--spacing-xs);
  }
}
