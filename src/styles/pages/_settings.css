/* ===================================
   SETTINGS PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.settings-container {
  margin: 0 auto;
  padding: var(--spacing-xl);
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* ===== SETTINGS HEADER ===== */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
}

.settings-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* ===== TABS CONTAINER ===== */
.settings-tabs-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
  min-height: 600px;
}

.settings-tabs-header {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
  margin-bottom: 0;
}

.settings-tab-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--text-secondary);
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.settings-tab-button:hover {
  color: var(--text-color);
  background-color: var(--hover-bg);
}

.settings-tab-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
}

.settings-tab-icon {
  font-size: 1.125rem;
}

.settings-tab-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
  background-color: var(--bg-color);
}

.settings-tab-panel {
  animation: fadeIn 0.3s ease-in-out;
}

/* ===== SETTINGS SECTIONS ===== */
.settings-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.settings-section {
  background-color: var(--section-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-md) var(--spacing-xl) 0 var(--spacing-xl);
  border: 1px solid var(--border-color);
}

.settings-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: var(--spacing-md) 0;
}

.settings-section-header h3 {
  margin: 0;
  padding: 0;
  border-bottom: none;
  font-size: 1.125rem;
  color: var(--text-color);
}

.settings-toggle-section-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.25rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs);
  border-radius: var(--radius-full);
  transition: background-color var(--transition-fast);
}

.settings-toggle-section-btn:hover {
  background-color: var(--hover-bg);
}

/* ===== THEME TOGGLE ===== */
.settings-theme-toggle {
  display: flex;
  gap: var(--spacing-md);
}

.settings-theme-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--input-bg);
  color: var(--text-color);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.settings-theme-btn.active {
  background-color: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.settings-theme-btn:hover {
  background-color: var(--btn-secondary-hover);
}

/* ===== USER MANAGEMENT ===== */
.settings-approvers-container {
  animation: fadeIn 0.3s ease-in-out;
}

.settings-request-type-section {
  margin-bottom: var(--spacing-2xl);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-lg);
}

.settings-request-type-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.settings-request-type-section h4 {
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
}

.settings-platform-config {
  margin: var(--spacing-lg) 0 var(--spacing-lg) var(--spacing-xl);
  padding: var(--spacing-lg);
  background-color: var(--input-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.settings-platform-config h5 {
  font-size: 0.875rem;
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

.settings-config-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-md);
}

.settings-config-item {
  flex: 1;
  min-width: 250px;
}

.settings-config-item label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  font-size: 0.8125rem;
  color: var(--text-color);
}

/* ===== USER INPUT AND SUGGESTIONS ===== */
.settings-user-input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--input-bg);
  color: var(--text-color);
  font-size: 0.97rem;
  outline: none;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  margin-right: var(--spacing-sm);
}

.settings-user-input:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.1);
}

.settings-user-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 200px;
  overflow-y: auto;
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  box-shadow: var(--card-shadow);
  z-index: var(--z-dropdown);
  margin-top: var(--spacing-xs);
}

.settings-user-suggestion-item {
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  color: var(--text-color);
  transition: background-color var(--transition-fast);
}

.settings-user-suggestion-item:hover {
  background-color: var(--hover-bg);
}

.settings-user-add-btn {
  min-width: 32px;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--btn-success-bg);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  font-size: 1.125rem;
  cursor: pointer;
  transition: background var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-user-add-btn:hover {
  background: var(--btn-success-hover);
}

/* ===== USER LIST ===== */
.settings-user-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xs);
}

.settings-user-item {
  display: flex;
  align-items: center;
  background-color: var(--filter-option-bg);
  color: var(--filter-option-text);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 0.85rem;
  font-weight: 500;
}

.settings-user-item span {
  margin-right: var(--spacing-sm);
}

.settings-user-item .btn-remove-option {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

/* ===== CONNECTION SETTINGS ===== */
.settings-connections-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.settings-connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--input-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.settings-connection-info {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.settings-connection-name {
  font-weight: 500;
  color: var(--text-color);
}

.settings-connection-type {
  padding: 2px var(--spacing-sm);
  background-color: var(--input-bg);
  color: var(--filter-option-text);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
}

.settings-connection-url {
  color: var(--text-color);
  opacity: 0.7;
  font-size: 0.875rem;
}

.settings-add-connection {
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
}

.settings-add-connection h4 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-color);
  font-size: 1rem;
}

.settings-connection-form {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr auto;
  gap: var(--spacing-md);
  align-items: center;
}

.settings-connection-form input,
.settings-connection-form select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .settings-tabs-header {
  background-color: var(--section-bg);
}

[data-theme="dark"] .settings-tab-button {
  color: var(--text-secondary);
}

[data-theme="dark"] .settings-tab-button:hover {
  color: var(--text-color);
  background-color: var(--hover-bg);
}

[data-theme="dark"] .settings-tab-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
}

[data-theme="dark"] .settings-section {
  background-color: var(--section-bg);
  border-color: var(--border-color);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .settings-platform-config {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .settings-user-input {
  background: var(--input-bg);
  color: var(--text-color);
  border-color: var(--border-color);
}

[data-theme="dark"] .settings-user-input:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(0, 51, 201, 0.15);
}

[data-theme="dark"] .settings-user-suggestions {
  background-color: var(--input-bg);
  border-color: var(--border-color);
  box-shadow: var(--card-shadow);
}

[data-theme="dark"] .settings-user-suggestion-item:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .settings-connection-item {
  background-color: var(--input-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .settings-connection-form input,
[data-theme="dark"] .settings-connection-form select {
  background-color: var(--input-bg);
  color: var(--text-color);
  border-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .settings-config-group {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .settings-config-item {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .settings-container {
    padding: var(--spacing-lg);
  }

  .settings-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
  }

  .settings-header h2 {
    text-align: center;
  }

  .settings-actions {
    justify-content: center;
  }

  .settings-tabs-header {
    overflow-x: auto;
    white-space: nowrap;
  }

  .settings-tab-content {
    padding: var(--spacing-lg);
  }

  .settings-section {
    padding: var(--spacing-md) var(--spacing-lg) 0 var(--spacing-lg);
  }

  .settings-platform-config {
    margin-left: 0;
    padding: var(--spacing-md);
  }

  .settings-connection-form {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .settings-theme-toggle {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .settings-theme-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .settings-container {
    padding: var(--spacing-md);
  }

  .settings-tabs-container {
    height: calc(100vh - 150px);
    min-height: 500px;
  }

  .settings-tab-button {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
  }

  .settings-tab-content {
    padding: var(--spacing-md);
  }

  .settings-section {
    padding: var(--spacing-sm) var(--spacing-md) 0 var(--spacing-md);
  }

  .settings-section-header h3 {
    font-size: 1rem;
  }

  .settings-user-list {
    gap: var(--spacing-xs);
  }

  .settings-user-item {
    font-size: 0.8125rem;
    padding: 2px var(--spacing-xs);
  }

  .settings-connection-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .settings-connection-type {
    font-size: 0.6875rem;
  }

  .settings-connection-url {
    font-size: 0.8125rem;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
