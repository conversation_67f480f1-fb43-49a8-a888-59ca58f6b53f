/* ===================================
   SEND FEEDBACK PAGE STYLES
   ================================= */

/* ===== PAGE CONTAINER ===== */
.send-feedback-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* ===== HEADER ===== */
.send-feedback-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
}

.send-feedback-header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex: 1;
}

.send-feedback-header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-green), var(--primary-green-dark));
  border-radius: var(--radius-lg);
  color: white;
  font-size: 1.5rem;
}

.send-feedback-header-text h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color);
}

.send-feedback-header-text p {
  margin: var(--spacing-xs) 0 0 0;
  font-size: 1rem;
  color: var(--text-muted);
}

/* ===== FORM CONTAINER ===== */
.send-feedback-form-container {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-2xl);
  border: 1px solid var(--border-color);
}

.send-feedback-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.send-feedback-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.send-feedback-form-group label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.875rem;
}

.send-feedback-form-group input,
.send-feedback-form-group select,
.send-feedback-form-group textarea {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.send-feedback-form-group input:focus,
.send-feedback-form-group select:focus,
.send-feedback-form-group textarea:focus {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(0, 207, 106, 0.1);
  outline: none;
}

.send-feedback-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

/* ===== DESCRIPTION EDITOR ===== */
.send-feedback-description-editor {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--input-bg);
}

.send-feedback-description-editor .ql-toolbar {
  border: none;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.send-feedback-description-editor .ql-container {
  border: none;
  min-height: 120px;
  font-size: 0.875rem;
}

/* ===== ATTACHMENT SECTION ===== */
.send-feedback-attachment-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.send-feedback-attachment-error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--error-bg);
  color: var(--error-text);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-sm);
  font-size: 0.8125rem;
  line-height: 1.4;
  animation: fadeIn 0.3s ease-in-out;
}

.send-feedback-attachment-error-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

.send-feedback-attachment-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
  width: fit-content;
}

.send-feedback-attachment-button:hover {
  border-color: var(--primary-green);
  color: var(--primary-green);
  background-color: rgba(0, 207, 106, 0.05);
}

.send-feedback-attachments-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.send-feedback-attachment-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: var(--section-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.8125rem;
  position: relative;
}

.send-feedback-attachment-item.uploading {
  border-color: var(--primary-green);
  background-color: rgba(0, 207, 106, 0.05);
}

.send-feedback-attachment-item.uploaded {
  border-color: var(--success-color);
  background-color: var(--success-bg);
}

.send-feedback-attachment-item.error {
  border-color: var(--error-color);
  background-color: var(--error-bg);
}

.send-feedback-attachment-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
  padding-right: 32px;
}

.send-feedback-attachment-name {
  flex: 1;
  color: var(--text-color);
  font-weight: 500;
}

.send-feedback-attachment-size {
  color: var(--text-muted);
}

.send-feedback-upload-progress {
  color: var(--primary-green);
  font-weight: 500;
  font-size: 0.75rem;
}

.send-feedback-upload-status {
  font-weight: 500;
  font-size: 0.75rem;
}

.send-feedback-upload-status.success {
  color: var(--success-color);
}

.send-feedback-upload-status.error {
  color: var(--error-color);
}

.send-feedback-progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-top: var(--spacing-xs);
}

.send-feedback-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green), var(--primary-green-dark));
  border-radius: var(--radius-sm);
  transition: width var(--transition-normal);
}

.send-feedback-file-error-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-sm);
  margin-top: var(--spacing-xs);
}

.send-feedback-error-text {
  flex: 1;
  color: var(--error-color);
  font-size: 0.75rem;
  line-height: 1.4;
}

.send-feedback-retry-upload-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--error-color);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  font-size: 0.6875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.send-feedback-retry-upload-btn:hover {
  background-color: var(--error-color-dark);
  transform: translateY(-1px);
}

.send-feedback-remove-attachment {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: var(--radius-sm);
  background-color: transparent;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
}

.send-feedback-remove-attachment:hover:not(:disabled) {
  background-color: var(--error-bg);
  color: var(--error-color);
}

.send-feedback-remove-attachment:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ===== FORM ACTIONS ===== */
.send-feedback-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
}

/* ===== MESSAGES ===== */
.send-feedback-error-message,
.send-feedback-success-message,
.send-feedback-info-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xl);
  font-size: 0.875rem;
  line-height: 1.5;
  animation: fadeIn 0.3s ease-in-out;
}

.send-feedback-error-message {
  background-color: var(--error-bg);
  color: var(--error-text);
  border: 1px solid var(--error-color);
}

.send-feedback-success-message {
  background-color: var(--success-bg);
  color: var(--success-text);
  border: 1px solid var(--success-color);
}

.send-feedback-info-message {
  background-color: var(--info-bg);
  color: var(--info-text);
  border: 1px solid var(--info-color);
  justify-content: space-between;
}

.send-feedback-clear-draft-btn {
  padding: var(--spacing-xs) var(--spacing-md);
  background-color: transparent;
  color: var(--info-color);
  border: 1px solid var(--info-color);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.send-feedback-clear-draft-btn:hover {
  background-color: var(--info-color);
  color: white;
}

.send-feedback-error-icon,
.send-feedback-success-icon,
.send-feedback-info-icon {
  font-size: 1.125rem;
  flex-shrink: 0;
}

/* ===== SUCCESS MODAL ===== */
.send-feedback-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.3s ease-in-out;
}

.send-feedback-modal-content {
  background-color: var(--content-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--modal-shadow);
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  animation: send-feedback-modalSlideIn 0.3s ease-out;
}

.send-feedback-modal-header {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-xl) var(--spacing-xl);
}

.send-feedback-modal-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-lg);
}

.send-feedback-modal-icon.success {
  background-color: var(--success-bg);
  color: var(--success-color);
  font-size: 2rem;
}

.send-feedback-modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.send-feedback-modal-description {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.send-feedback-modal-body {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
}

.send-feedback-modal-actions {
  padding: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

/* ===== ANIMATIONS ===== */
@keyframes send-feedback-modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== DARK THEME SUPPORT ===== */
[data-theme="dark"] .send-feedback-form-container {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .send-feedback-description-editor .ql-toolbar {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .send-feedback-description-editor .ql-container {
  background-color: var(--input-bg);
  color: var(--text-color);
}

[data-theme="dark"] .send-feedback-attachment-item {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .send-feedback-modal-content {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .send-feedback-container {
    padding: var(--spacing-lg);
  }

  .send-feedback-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-lg);
  }

  .send-feedback-header-content {
    width: 100%;
  }

  .send-feedback-form-container {
    padding: var(--spacing-xl);
  }

  .send-feedback-form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .send-feedback-form-actions {
    flex-direction: column-reverse;
  }

  .send-feedback-modal-content {
    margin: var(--spacing-lg);
    max-width: calc(100% - 32px);
  }
}

@media (max-width: 480px) {
  .send-feedback-container {
    padding: var(--spacing-md);
  }

  .send-feedback-form-container {
    padding: var(--spacing-lg);
  }

  .send-feedback-header-text h1 {
    font-size: 1.5rem;
  }

  .send-feedback-modal-header {
    padding: var(--spacing-xl) var(--spacing-md) var(--spacing-lg);
  }

  .send-feedback-modal-body,
  .send-feedback-modal-actions {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  .send-feedback-modal-icon {
    width: 64px;
    height: 64px;
    font-size: 1.75rem;
  }

  .send-feedback-modal-title {
    font-size: 1.25rem;
  }
}
